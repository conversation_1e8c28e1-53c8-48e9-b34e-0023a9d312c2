import { backend_base_url } from "./api";
import { getHeadersRaw } from "./api";


/**
 * Lists deployments for a specific tenant
 * @param {string} tenantId - The ID of the tenant
 * @param {string} dbName - The database name
 * @param {string} collectionName - The collection name
 * @returns {Promise} - Promise resolving to the list of deployments
 */
export const listDeployments = async (projectId) => {
  try {
    const url = `${backend_base_url}/deployment/${projectId}/list_deployments`;
    
    console.log('Fetching deployments from:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeadersRaw(),
    });
    
    console.log('Deployments API response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Deployments API error:', response.status, errorText);
      throw new Error(`Error listing deployments: ${response.statusText} - ${errorText}`);
    }
    
    const data = await response.json();
    console.log('Deployments API response data:', data);
    
    // Ensure we return an array
    if (Array.isArray(data)) {
      return data;
    } else if (data && Array.isArray(data.deployments)) {
      return data.deployments;
    } else if (data && Array.isArray(data.data)) {
      return data.data;
    } else {
      console.warn('Unexpected deployments response format:', data);
      return [];
    }
  } catch (error) {
    console.error('Error in listDeployments:', error);
    throw error;
  }
};


/**
 * Deletes a specific deployment
 * @param {string} tenantId - The ID of the tenant
 * @param {string} deploymentId - The ID of the deployment to delete
 * @param {string} dbName - The database name
 * @param {string} collectionName - The collection name
 * @returns {Promise} - Promise resolving to the deletion result
 */
export const deleteDeployment = async (projectId, appId) => {
  try {
    const url = `${backend_base_url}/deployment/${projectId}/delete_deployment/${appId}`;
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: getHeadersRaw(),
    });
    
    if (!response.ok) {
      throw new Error(`Error deleting deployment: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};



