// utils/repositoryAPI.js
"use client";

import { getHeaders } from "./api";
import { decryptString } from "./hash";
const base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'repository';

// Create a new repository
export const createRepository = async (projectId, containerId, scmId, options = {}) => {
  try {
    const queryParams = new URLSearchParams({
      container_id: containerId,
      scm_id: scmId,
      ...(options.repository_name && { repository_name: options.repository_name }),
      ...(options.is_private !== undefined && { is_private: options.is_private }),
      ...(options.force_new !== undefined && { force_new: options.force_new })
    });

    const response = await fetch(
      `${base_url}/${SHOW_NAME}/create_repository/${projectId}/?${queryParams}`,
      {
        method: "POST",
        headers: await getHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to create repository");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const createBranch = async (projectId, containerId, newBranchName, sourceBranch = "main") => {
  try {
    const response = await fetch(
      `${base_url}/${SHOW_NAME}/create_branch/${projectId}/?` + 
      `container_id=${containerId}&` +
      `new_branch_name=${newBranchName}&` +
      `source_branch=${sourceBranch}`, 
      {
        method: "POST",
        headers: await getHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to create branch");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Get repository details
export const getRepository = async (projectId, containerId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/get_repository/${projectId}/?container_id=${containerId}`, {
      method: "GET",
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error("Failed to fetch repository details");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// List all repositories with pagination
export const listRepositories = async (scmType, scmId = "", decrypt = true, page = 1, perPage = 30, filters = {}) => {
  try {
    let url = new URL(`${base_url}/${SHOW_NAME}/list_repositories/${scmType}`);
    
    // Add query parameters
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString()
    });
    
    if (scmId) {
      if (decrypt) {
        let decryptedScmId = decryptString(scmId);
        params.append('scm_id', decryptedScmId);
      } else {
        params.append('scm_id', scmId);
      }
    }
    
    // Add filters if provided
    if (filters.search?.trim()) {
      params.append('search', filters.search.trim());
    }
    if (filters.organization?.trim()) {
      params.append('organization', filters.organization.trim());
    }
    if (filters.visibility) {
      params.append('visibility', filters.visibility);
    }
    
    url.search = params.toString();
    
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders()
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to list repositories");
    }
    
    const data = await response.json();
    
    return data;
  } catch (error) {
    
    throw error;
  }
};

// List project repositories
export const listProjectRepositories = async (projectId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/list_project_repositories/${projectId}/`, {
      method: "GET",
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error("Failed to list project repositories");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Delete repository
export const deleteRepository = async (projectId, containerId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/delete_repository/${projectId}/`, {
      method: "DELETE",
      headers: await getHeaders(),
      body: JSON.stringify({
        container_id: containerId
      })
    });

    if (!response.ok) {
      throw new Error("Failed to delete repository");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const updateRepositoryName = async (projectId, containerId, newName) => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/update_repository/${projectId}/`, {
            method: "PUT",
            headers: await getHeaders(),
            body: JSON.stringify({
                container_id: parseInt(containerId),
                new_name: newName
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || "Failed to update repository name");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};

/**
 * Update a specific field in the repository collection
 * @param {number} projectId - The project ID
 * @param {string} fieldName - Name of the field to update
 * @param {string|number|boolean|object} fieldValue - Value to set for the field
 * @returns {Promise<Object>} Response from the API
 */
export const updateRepositoryField = async (projectId, fieldName, fieldValue) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/update_repository_field/${projectId}/`, {
      method: "PUT",
      headers: await getHeaders(),
      body: JSON.stringify({
        field_name: fieldName,
        field_value: fieldValue
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || errorData.error || "Failed to update repository field");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Convenience functions for common field updates
 */
export const updateRepositoryManifest = async (projectId, manifestData) => {
  return updateRepositoryField(projectId, "project_manifest", manifestData);
};

export const updateRepositoryUrl = async (projectId, url) => {
  return updateRepositoryField(projectId, "repository_url", url);
};

export const updateRepositoryBranch = async (projectId, branch) => {
  return updateRepositoryField(projectId, "default_branch", branch);
};

export const updateRepositoryStatus = async (projectId, status) => {
  return updateRepositoryField(projectId, "status", status);
};

export const listAllBranches = async (projectId, containerId, page = 1, perPage = 30, search = '') => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/all_branches/${projectId}/?container_id=${containerId}&page=${page}&per_page=${perPage}&search=${search}`, {
      method: "GET",
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error("Failed to fetch repository branches");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const changeDefaultBranch = async (projectId, containerId, newDefaultBranch) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/change_default_branch/${projectId}/?container_id=${containerId}&new_default_branch=${newDefaultBranch}`, {
      method: "PUT",
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to change default branch");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const linkRepository = async (projectId, containerId, scmId, repositoryMetadata) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/link_repository/${projectId}/?container_id=${containerId}`, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        container_id: containerId,
        scm_id: scmId,
        ...repositoryMetadata
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to link repository");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};


// Additional connector functions for repositoryAPI.js

/**
 * Get a specific field value from repository record
 * @param {number} projectId - Project ID
 * @param {string} fieldName - Name of the field to retrieve
 * @returns {Promise<Object>} Field value and metadata
 */
export const getRepositoryField = async (projectId, fieldName) => {
  try {
    const queryParams = new URLSearchParams({ field_name: fieldName });
    
    const response = await fetch(
      `${base_url}/${SHOW_NAME}/get_repository_field/${projectId}/?${queryParams}`,
      {
        method: "GET",
        headers: await getHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to get repository field");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Get complete repository record from database
 * @param {number} projectId - Project ID
 * @returns {Promise<Object>} Complete repository record
 */
export const getRepositoryRecord = async (projectId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/get_repository_record/${projectId}/`, {
      method: "GET",
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to get repository record");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Generate project manifest with Server-Sent Events streaming
 * @param {number} projectId - Project ID
 * @param {Function} onMessage - Callback for each message
 * @param {Function} onError - Callback for errors
 * @param {Function} onComplete - Callback for completion
 * @returns {Promise<void>} Streaming promise
 */
export const generateProjectManifest = async (projectId, onMessage, onError, onComplete) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/generate_manifest/${projectId}/`, {
      method: "GET",
      headers: {
        ...await getHeaders(),
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to generate manifest");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            onMessage(data);
            
            if (data.status === 'complete' || data.status === 'error') {
              onComplete(data);
              return;
            }
          } catch (error) {
            onError(error);
          }
        }
      }
    }
  } catch (error) {
    onError(error);
  }
};


// Branch Manifest API Connector Functions
// Add these to your existing repositoryAPI.js file

/**
 * Get all branch project manifests for a given project
 * @param {number} projectId - Project ID
 * @returns {Promise<Object>} Object containing all branch manifests with metadata
 */
export const getAllBranchManifests = async (projectId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/get_all_branch_manifests/${projectId}/`, {
      method: "GET",
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to get all branch manifests");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Get a specific branch project manifest
 * @param {number} projectId - Project ID
 * @param {string} repositoryId - Name of the repository
 * @param {string} branchName - Name of the branch
 * @returns {Promise<Object>} Branch manifest data
 */
export const getBranchManifest = async (projectId, repositoryId, branchName) => {
  try {
    const queryParams = new URLSearchParams({
      repository_id: repositoryId,
      branch_name: branchName
    });

    const response = await fetch(
      `${base_url}/${SHOW_NAME}/get_branch_manifest/${projectId}/?${queryParams}`,
      {
        method: "GET",
        headers: await getHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to get branch manifest");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Update project manifest for a specific branch
 * @param {number} projectId - Project ID
 * @param {string} repositoryId - Id of the repository
 * @param {string} branchName - Name of the branch
 * @param {string} projectManifest - Project manifest content in YAML format
 * @returns {Promise<Object>} Update response
 */
export const updateBranchManifest = async (projectId, repositoryId, branchName, projectManifest) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/update_branch_manifest/${projectId}/`, {
      method: "PUT",
      headers: await getHeaders(),
      body: JSON.stringify({
        repository_id: repositoryId,
        branch_name: branchName,
        project_manifest: projectManifest
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update branch manifest");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Delete project manifest for a specific branch
 * @param {number} projectId - Project ID
 * @param {string} repositoryName - Name of the repository
 * @param {string} branchName - Name of the branch
 * @returns {Promise<Object>} Delete response
 */
export const deleteBranchManifest = async (projectId, repositoryName, branchName) => {
  try {
    const queryParams = new URLSearchParams({
      repository_name: repositoryName,
      branch_name: branchName
    });

    const response = await fetch(
      `${base_url}/${SHOW_NAME}/delete_branch_manifest/${projectId}/?${queryParams}`,
      {
        method: "DELETE",
        headers: await getHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to delete branch manifest");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Convenience function to get manifest for a specific repository's default branch
 * @param {number} projectId - Project ID
 * @param {string} repositoryName - Name of the repository
 * @param {string} defaultBranch - Default branch name (defaults to "main")
 * @returns {Promise<Object>} Branch manifest data
 */
export const getDefaultBranchManifest = async (projectId, repositoryName, defaultBranch = "main") => {
  return getBranchManifest(projectId, repositoryName, defaultBranch);
};

/**
 * Convenience function to update manifest for a specific repository's default branch
 * @param {number} projectId - Project ID
 * @param {string} repositoryName - Name of the repository
 * @param {string} projectManifest - Project manifest content
 * @param {string} defaultBranch - Default branch name (defaults to "main")
 * @returns {Promise<Object>} Update response
 */
export const updateDefaultBranchManifest = async (projectId, repositoryName, projectManifest, defaultBranch = "main") => {
  return updateBranchManifest(projectId, repositoryName, defaultBranch, projectManifest);
};

/**
 * Get manifests for all branches of a specific repository
 * @param {number} projectId - Project ID
 * @param {string} repositoryName - Name of the repository
 * @returns {Promise<Array>} Array of branch manifests for the specified repository
 */
export const getRepositoryBranchManifests = async (projectId, repositoryName) => {
  try {
    const allManifests = await getAllBranchManifests(projectId);
    
    // Filter manifests for the specified repository
    const repositoryManifests = allManifests.manifests.filter(
      manifest => manifest.repository_name === repositoryName
    );
    
    return {
      project_id: projectId,
      repository_name: repositoryName,
      total_manifests: repositoryManifests.length,
      manifests: repositoryManifests
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Bulk update manifests for multiple branches
 * @param {number} projectId - Project ID
 * @param {Array} manifestUpdates - Array of objects with {repositoryName, branchName, projectManifest}
 * @returns {Promise<Array>} Array of update responses
 */
export const bulkUpdateBranchManifests = async (projectId, manifestUpdates) => {
  try {
    const updatePromises = manifestUpdates.map(update => 
      updateBranchManifest(
        projectId,
        update.repositoryName,
        update.branchName,
        update.projectManifest
      )
    );

    const results = await Promise.allSettled(updatePromises);
    
    return results.map((result, index) => ({
      repository_name: manifestUpdates[index].repositoryName,
      branch_name: manifestUpdates[index].branchName,
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason.message : null
    }));
  } catch (error) {
    throw error;
  }
};

/**
 * Check if a branch has a project manifest
 * @param {number} projectId - Project ID
 * @param {string} repositoryName - Name of the repository
 * @param {string} branchName - Name of the branch
 * @returns {Promise<boolean>} True if manifest exists, false otherwise
 */
export const hasBranchManifest = async (projectId, repositoryName, branchName) => {
  try {
    await getBranchManifest(projectId, repositoryName, branchName);
    return true;
  } catch (error) {
    if (error.message.includes('No project manifest found') || 
        error.message.includes('404')) {
      return false;
    }
    throw error;
  }
};

/**
 * Get branch manifest with fallback to default branch
 * @param {number} projectId - Project ID
 * @param {string} repositoryName - Name of the repository
 * @param {string} branchName - Name of the branch
 * @param {string} fallbackBranch - Fallback branch name (defaults to "main")
 * @returns {Promise<Object>} Branch manifest data
 */
export const getBranchManifestWithFallback = async (projectId, repositoryName, branchName, fallbackBranch = "main") => {
  try {
    return await getBranchManifest(projectId, repositoryName, branchName);
  } catch (error) {
    if (error.message.includes('No project manifest found') || 
        error.message.includes('404')) {
      
      // Try fallback branch if different from requested branch
      if (branchName !== fallbackBranch) {
        try {
          return await getBranchManifest(projectId, repositoryName, fallbackBranch);
        } catch (fallbackError) {
          throw new Error(`No manifest found for branch '${branchName}' or fallback branch '${fallbackBranch}'`);
        }
      }
    }
    throw error;
  }
};

/**
 * Alternative EventSource implementation for generateProjectManifest
 * (Use this if you prefer EventSource over fetch streaming)
 * @param {number} projectId - Project ID
 * @returns {EventSource} EventSource instance
 */
export const generateProjectManifestEventSource = (projectId) => {
  const url = `${base_url}/${SHOW_NAME}/generate_manifest/${projectId}/`;
  return new EventSource(url);
};

/**
 * Utility function to handle manifest generation with callbacks
 * @param {number} projectId - Project ID
 * @param {Object} callbacks - Object containing onMessage, onError, onComplete callbacks
 * @returns {Promise<void>} 
 */
export const handleManifestGeneration = async (projectId, callbacks) => {
  const { onMessage, onError, onComplete } = callbacks;
  
  try {
    await generateProjectManifest(
      projectId,
      onMessage || ((data) => console.log('Manifest message:', data)),
      onError || ((error) => console.error('Manifest error:', error)),
      onComplete || ((result) => console.log('Manifest complete:', result))
    );
  } catch (error) {
    if (onError) onError(error);
    throw error;
  }
};


/**
 * Get project manifest with optional filtering
 * @param {number} projectId - Project ID
 * @param {Array<string>} containerIds - Optional list of container IDs to include
 * @param {boolean} allRepositories - Include all repositories in manifest
 * @returns {Promise<Object>} Project manifest data
 */
export const getProjectManifest = async (projectId, containerIds = null, allRepositories = false) => {
  try {
    const queryParams = new URLSearchParams({
      all_repositories: allRepositories.toString()
    });
    
    // Add container_ids as repeated query parameters if provided
    if (containerIds && Array.isArray(containerIds)) {
      containerIds.forEach(id => {
        queryParams.append('container_ids', id);
      });
    }
    
    const response = await fetch(
      `${base_url}/${SHOW_NAME}/get_project_manifest/${projectId}/?${queryParams}`,
      {
        method: "GET",
        headers: await getHeaders()
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to get project manifest");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Update project manifest
 * @param {number} projectId - Project ID
 * @param {string} projectManifest - Project manifest content in YAML format
 * @returns {Promise<Object>} Update response
 */
export const updateProjectManifest = async (projectId, projectManifest) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/update_project_manifest/${projectId}/`, {
      method: "PUT",
      headers: await getHeaders(),
      body: JSON.stringify({
        project_manifest: projectManifest
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update project manifest");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};