import React, { useState, useRef, useEffect } from "react";
import ReactDOM from "react-dom";

// File Selector Component (App Theme)
const AttachmentSelector = ({
  isVisible,
  position,
  attachments,
  onSelect,
  onClose,
  searchQuery = ""
}) => {
  const selectorRef = useRef(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [hoveredIndex, setHoveredIndex] = useState(-1);

  // Filter attachments based on search query, prioritizing figma attachments
  const filteredAttachments = attachments.filter(attachment => {
    if (!searchQuery) return true;
    const filename = attachment.filename || attachment.name || '';
    return filename.toLowerCase().includes(searchQuery.toLowerCase());
  }).sort((a, b) => {
    // Prioritize figma attachments
    const aIsFigma = a.file_type && a.file_type.includes('figma');
    const bIsFigma = b.file_type && b.file_type.includes('figma');
    
    if (aIsFigma && !bIsFigma) return -1;
    if (!aIsFigma && bIsFigma) return 1;
    return 0;
  });

  // Reset selected index when filtered attachments change
  useEffect(() => {
    setSelectedIndex(0);
    setHoveredIndex(-1);
  }, [filteredAttachments.length, searchQuery]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isVisible || filteredAttachments.length === 0) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, filteredAttachments.length - 1));
          setHoveredIndex(-1); // Clear hover when using keyboard
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          setHoveredIndex(-1); // Clear hover when using keyboard
          break;
        case 'Enter':
          event.preventDefault();
          if (filteredAttachments[selectedIndex]) {
            onSelect(filteredAttachments[selectedIndex]);
          }
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, filteredAttachments, selectedIndex, onSelect, onClose]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return ReactDOM.createPortal(
    <div
      ref={selectorRef}
      className="fixed z-[1000] bg-white rounded-xl shadow-2xl border border-gray-200 max-w-lg w-96"
      style={{
        bottom: position.bottom,
        left: position.left,
        maxHeight: '280px',
      }}
    >
      <div className="p-3">
        <div className="text-xs text-gray-500 font-medium px-2 py-2 border-b border-gray-100">
          Select a file {searchQuery && `matching "${searchQuery}"`}
        </div>
        <div className="max-h-56 overflow-y-auto py-2" style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#d1d5db #f9fafb'
        }}>
          {filteredAttachments.length === 0 ? (
            <div className="px-3 py-6 text-sm text-gray-500 text-center">
              {searchQuery ? `No files match "${searchQuery}"` : "No files available"}
            </div>
          ) : (
            filteredAttachments.map((attachment, index) => {
              const isSelected = selectedIndex === index;
              const isHovered = hoveredIndex === index;
              const isHighlighted = isSelected && hoveredIndex === -1; // Highlight when keyboard selected and not hovering
              
              return (
                <button
                  key={`${attachment.id}-${index}`}
                  onClick={() => onSelect(attachment)}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(-1)}
                  className={`w-full text-left px-3 py-3 text-sm rounded-lg flex items-center gap-3 group transition-colors duration-150 ${
                    isHighlighted || isHovered
                      ? 'bg-primary-50 text-primary-900 border border-primary-200'
                      : 'hover:bg-gray-50 text-gray-900'
                  }`}
                >
                  {/* Icon based on file type */}
                  <div className={`flex-shrink-0 w-8 h-8 rounded-md flex items-center justify-center ${
                    attachment.file_type && attachment.file_type.includes('figma')
                      ? 'bg-purple-100' 
                      : 'bg-gray-100'
                  }`}>
                    {attachment.file_type && attachment.file_type.includes('figma') ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 32 48" fill="none">
                        <path d="M16 0H8C3.582 0 0 3.582 0 8s3.582 8 8 8h8V0z" fill="#F24E1E" />
                        <path d="M16 16H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V16z" fill="#A259FF" />
                        <path d="M16 32H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V32z" fill="#1ABCFE" />
                        <path d="M16 0h8c4.418 0 8 3.582 8 8s-3.582 8-8 8h-8V0z" fill="#FF7262" />
                        <circle cx="16" cy="24" r="8" fill="#0ACF83" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                    )}
                  </div>
                
                  <div className="flex-1 min-w-0 flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className={`truncate font-medium ${
                        isHighlighted || isHovered ? 'text-primary-900' : 'text-gray-900'
                      }`}>
                        {attachment.filename || attachment.name}
                      </div>
                      {attachment.file_type && (
                        <div className="text-xs text-gray-500">
                          {attachment.file_type.replace('_', ' ')}
                        </div>
                      )}
                    </div>
                    <div className={`text-xs ml-4 flex-shrink-0 ${
                      isHighlighted || isHovered ? 'text-primary-600' : 'text-gray-500'
                    }`}>
                      {attachment.path}
                    </div>
                  </div>
                </button>
              );
            })
          )}
        </div>
      </div>
    </div>,
    document.body
  );
};

export default AttachmentSelector;