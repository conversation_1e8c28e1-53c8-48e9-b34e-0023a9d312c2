"use client";

import React, { useState, useContext, useEffect } from "react";
import Link from "next/link";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { loginUser, getOrganizationNameById, getUser, getUserOrganizations } from "../../../../utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { decryptTenantId, encryptTenantId, getRootTenantId } from "@/utils/hash";
import LoginSignupContainer from "@/components/LoginSignupContainer";
import { ArrowRightIcon, Search } from "lucide-react";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

// Add these helper functions at the top of your component
const storeTenantInfoByEmail = (email, tenantInfo) => {
  if (!email) return;
  const emailKey = email.toLowerCase().trim();
  const tenantsData = JSON.parse(localStorage.getItem('tenantsByEmail') || '{}');
  tenantsData[emailKey] = tenantInfo;
  localStorage.setItem('tenantsByEmail', JSON.stringify(tenantsData));
};

const getTenantInfoByEmail = (email) => {
  if (!email) return null;
  const emailKey = email.toLowerCase().trim();
  const tenantsData = JSON.parse(localStorage.getItem('tenantsByEmail') || '{}');
  return tenantsData[emailKey] || null;
};

const LoginPage = () => {
  const { showAlert } = useContext(AlertContext)
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const params = useParams();
  const queryParams = useSearchParams();
  const [tenant_id, setTenant_id] = useState(null);
  const [rawTenantId, setRawTenantId] = useState(null);
  const [tenant_name, setTenant_name] = useState(null);
  const [isLoadingTenant, setIsLoadingTenant] = useState(false);
  const [organizations, setOrganizations] = useState([]);
  const [selectedOrg, setSelectedOrg] = useState('');
  const [showOrgDropdown, setShowOrgDropdown] = useState(false);
  const [isSearchingOrgs, setIsSearchingOrgs] = useState(false);
  const [hasSearchedForOrgs, setHasSearchedForOrgs] = useState(false);
  const [hadInitialTenantId, setHadInitialTenantId] = useState(false);
  const [isOrgDropdownOpen, setIsOrgDropdownOpen] = useState(false);
  const [orgSearchTerm, setOrgSearchTerm] = useState('');
  const [isNote, setNote] = useState(false);
  const filteredOrganizations = organizations.filter(org => {
    const displayName = org.name === "Default" ? "Kavia Common" : org.name;
    return displayName.toLowerCase().includes(orgSearchTerm.toLowerCase());
  });

  // Add this useEffect for handling clicks outside the dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (isOrgDropdownOpen && !event.target.closest('.org-dropdown-container')) {
        setIsOrgDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOrgDropdownOpen]);

  // Add this useEffect after your existing useEffects
useEffect(() => {
  // Set default Kavia Common tenant if no tenant is selected
  if (!tenant_id && !rawTenantId) {
    const rawId = process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID;
    const _tenant_id = encryptTenantId(rawId);
    setTenant_id(_tenant_id);
    setRawTenantId(rawId);
    setTenant_name("Kavia Common");
    setSelectedOrg(rawId);
    setOrganizations([{
      "id": process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID,
      "name": "Kavia Common"
    }]);
  }
}, []); // Empty dependency array means this runs once when component mounts
  // Update handleOrgChange function
  const handleOrgChange = (e) => {
    const newOrgId = e.target.value;
    setSelectedOrg(newOrgId);

    // Only change tenant if we didn't have a tenant_id in the URL initially
    // or if user explicitly selected a different org from the dropdown
    if (newOrgId && verifyEmail(username) && (!hadInitialTenantId || newOrgId !== rawTenantId)) {
      // Update tenant ID in state
      const encryptedId = encryptTenantId(newOrgId);
      setTenant_id(encryptedId);
      setRawTenantId(newOrgId);

      // If matching organization exists, set tenant name
      const selectedOrganization = organizations.find(org => org.id === newOrgId);
      if (selectedOrganization) {
        const tenantName = selectedOrganization.name === "Default" ? "Kavia Common" : selectedOrganization.name;
        setTenant_name(tenantName);

        // Store tenant info for this email
        storeTenantInfoByEmail(username, {
          pre_tenant_id: newOrgId,
          encrypt_tenant_id: encryptedId,
          pre_tenant_name: tenantName
        });
      }

      // Update URL only if the new tenant_id is the root tenant ID
      if (newOrgId === process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('tenant_id', newOrgId);
        window.history.replaceState({}, '', currentUrl.toString());
        setHadInitialTenantId(true);
      } else {
        // Remove tenant_id from URL if it exists
        const currentUrl = new URL(window.location.href);
        if (currentUrl.searchParams.has('tenant_id')) {
          currentUrl.searchParams.delete('tenant_id');
          window.history.replaceState({}, '', currentUrl.toString());
        }
        setHadInitialTenantId(false);
      }
    }
  };

  // Update fetchOrganizations function
  const fetchOrganizations = async (email) => {
    if (!verifyEmail(email)) {
      showAlert("Please enter a valid email address", "danger");
      return;
    }

    try {
      setIsSearchingOrgs(true);
      const response = await getUserOrganizations(email);

      if (response?.organizations?.length > 0) {
        setOrganizations(response.organizations);

        // If tenant_id was provided in URL, we'll show organizations but keep the selected tenant
        // unless user explicitly changes it
        if (!hadInitialTenantId) {
          // Get stored tenant info for this email
          const storedTenantInfo = getTenantInfoByEmail(email);
          const storedPreTenantId = storedTenantInfo?.pre_tenant_id;

          if (storedPreTenantId || rawTenantId) {
            let currentOrg = null;
            const tenantToCheck = storedPreTenantId || rawTenantId;

            if (tenantToCheck == process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID && response.organizations.length > 1) {
              currentOrg = response.organizations.find(org => org.id.startsWith("default") === tenantToCheck || org.id === tenantToCheck);
            } else {
              currentOrg = response.organizations.find(org => org.id === tenantToCheck);
            }

            if (currentOrg) {
              setSelectedOrg(currentOrg.id);
              const encryptedId = encryptTenantId(currentOrg.id);
              setTenant_id(encryptedId);
              setRawTenantId(currentOrg.id);
              const tenantName = currentOrg.name === "Default" ? "Kavia Common" : currentOrg.name;
              setTenant_name(tenantName);

              // Store tenant info for this email
              storeTenantInfoByEmail(email, {
                pre_tenant_id: currentOrg.id,
                encrypt_tenant_id: encryptedId,
                pre_tenant_name: tenantName
              });
            } else {
              const firstOrg = response.organizations[0];
              setSelectedOrg(firstOrg.id);
              const encryptedId = encryptTenantId(firstOrg.id);
              setTenant_id(encryptedId);
              setRawTenantId(firstOrg.id);
              const tenantName = firstOrg.name === "Default" ? "Kavia Common" : firstOrg.name;
              setTenant_name(tenantName);

              storeTenantInfoByEmail(email, {
                pre_tenant_id: firstOrg.id,
                encrypt_tenant_id: encryptedId,
                pre_tenant_name: tenantName
              });
            }
          }
        } else {
          // If URL had tenant_id, just populate the organizations dropdown
          // without changing the selected tenant
          if (rawTenantId) {
            const matchingOrg = response.organizations.find(org => org.id === rawTenantId);
            if (matchingOrg) {
              setSelectedOrg(rawTenantId);
            }
          }
        }

        // Always show org dropdown if multiple orgs exist
        setShowOrgDropdown(response.organizations.length >= 2);
        setHasSearchedForOrgs(true);
      } else {
        // Handle Kavia Common case - only if no tenant_id was in URL
        if (!hadInitialTenantId) {
          const rawId = process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID;
          const _tenant_id = encryptTenantId(rawId);
          setTenant_id(_tenant_id);
          setRawTenantId(rawId);
          setTenant_name("Kavia Common");
          setOrganizations([{
            "id": process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID,
            "name": "Kavia Common"
          }]);
          setSelectedOrg(process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID);
          setShowOrgDropdown(false);
          setHasSearchedForOrgs(true);

          // Store Kavia Common info for this email
          storeTenantInfoByEmail(email, {
            pre_tenant_id: rawId,
            encrypt_tenant_id: _tenant_id,
            pre_tenant_name: "Kavia Common"
          });
        }

        if(isNote){
          showAlert("No organizations found for this email address. Kindly Signup to continue with Kavia Common.", "info");
        }
      }
    } catch (error) {

      showAlert("Failed to fetch organizations.", "warning");

      // Add default Kavia Common organization on error - only if no tenant_id was in URL
      if (!hadInitialTenantId) {
        const rawId = process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID;
        const _tenant_id = encryptTenantId(rawId);
        setTenant_id(_tenant_id);
        setRawTenantId(rawId);
        setTenant_name("Kavia Common");
        setOrganizations([{
          "id": process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID,
          "name": "Kavia Common"
        }]);
        setSelectedOrg(process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID);
        setShowOrgDropdown(false);
        setHasSearchedForOrgs(true);

        // Store Kavia Common info for this email
        storeTenantInfoByEmail(email, {
          pre_tenant_id: rawId,
          encrypt_tenant_id: _tenant_id,
          pre_tenant_name: "Kavia Common"
        });
      }
    } finally {
      setIsSearchingOrgs(false);
    }
  };

  // Update the useEffect that loads initial data
  useEffect(() => {
    const email = queryParams.get("email");
    const urlTenantId = queryParams.get("tenant_id");

    // Handle email from query params
    if (email && verifyEmail(email)) {
      setUsername(email);
      const storedTenantInfo = getTenantInfoByEmail(email);
      if (storedTenantInfo) {
        setTenant_id(storedTenantInfo.encrypt_tenant_id);
        setRawTenantId(storedTenantInfo.pre_tenant_id);
        setTenant_name(storedTenantInfo.pre_tenant_name);
      }
    }

    // Handle tenant_id from query params - this takes precedence
    if (urlTenantId) {
      // Remove tenant_id from URL and state if it's not the root tenant ID
      if (urlTenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
        // Remove from URL
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.delete('tenant_id');
        window.history.replaceState({}, '', currentUrl.toString());

        // Set default Kavia Common tenant instead
        const rawId = process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID;
        const _tenant_id = encryptTenantId(rawId);
        setTenant_id(_tenant_id);
        setRawTenantId(rawId);
        setTenant_name("Kavia Common");
        setSelectedOrg(rawId);
        setHadInitialTenantId(false);
      } else {
        // Only set tenant state if it's the root tenant ID
        const encryptedId = encryptTenantId(urlTenantId);
        setTenant_id(encryptedId);
        setRawTenantId(urlTenantId);
        setHadInitialTenantId(true);

        // Fetch tenant name
        getOrganizationNameById(urlTenantId)
          .then(response => {
            // Check for both response structures (tenant_name or name)
            if (response) {
              const orgName = response.name || response.tenant_name;
              if (orgName) {
                const tenantName = orgName === "Default" ? "Kavia Common" : orgName;
                setTenant_name(tenantName);
              }
            }
          })
          .catch(error => {
            // Default to "Kavia Common" if tenant name fetch fails and it's the B2C client ID
            if (urlTenantId === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) {
              setTenant_name("Kavia Common");
            }
          });
      }
    }
  }, [queryParams]);

  useEffect(() => {
    if(verifyEmail(username)){
      // Allow organization search as long as it's not the superadmin tenant
      if (rawTenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
        fetchOrganizations(username.trim());
      }
    }
  },[username]);

  // Update verifyEmail function to be more robust
  const verifyEmail = (email) => {
    if (!email) return false;
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email.trim());
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    // Validate required fields first
    if (!password) {
      showAlert("Please enter your password", "danger");
      return;
    }

    if (!verifyEmail(username)) {
      showAlert("Please enter a valid email address", "danger");
      return;
    }

    // If organization is selected but tenant_id is not set yet, set it first
    if (selectedOrg && (!tenant_id || decryptTenantId(tenant_id) !== selectedOrg)) {
      const encryptedId = encryptTenantId(selectedOrg);
      setTenant_id(encryptedId);
      setRawTenantId(selectedOrg);

      // Update URL only if it's the root tenant ID
      if (selectedOrg === process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
        // Update URL with new tenant_id (without page refresh)
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('tenant_id', selectedOrg);
        window.history.replaceState({}, '', currentUrl.toString());
        setHadInitialTenantId(true);
      } else {
        // Remove tenant_id from URL if it exists
        const currentUrl = new URL(window.location.href);
        if (currentUrl.searchParams.has('tenant_id')) {
          currentUrl.searchParams.delete('tenant_id');
          window.history.replaceState({}, '', currentUrl.toString());
        }
        setHadInitialTenantId(false);
      }
    }

    // Ensure we have a tenant_id before proceeding
    const finalTenantId = tenant_id || (selectedOrg ? encryptTenantId(selectedOrg) : null);
    if (!finalTenantId) {
      showAlert("Tenant ID is required!", "danger");
      return;
    }

    try {
      setIsLoading(true);

      const response = await loginUser(finalTenantId, username, password);

      if (response.status === 200 && !response.error) {
        try {
          // After successful login, check tenant status with getUser
          const userData = await getUser();

          // Check for inactive tenant
          if (userData.error && userData.errorType === "INACTIVE_TENANT" &&
            rawTenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
            // Set the inactive_tenant cookie and redirect to warning page
            document.cookie = "inactive_tenant=true; path=/";
            router.push("/warning?signin=true");
            return;
          }

          // For the example response provided in the user query
          if (response.id_token && response.refresh_token) {
            // Store the login response data in cookies instead of localStorage
            document.cookie = `loginResponseData=${JSON.stringify(response)}; path=/; max-age=604800`; // 1-week expiration

            // Check if tenant ID starts with 'default-' and redirect accordingly
            if (rawTenantId && rawTenantId.startsWith('default')) {
              window.location.href = "/";
              return;
            }

            // Otherwise redirect to the handle-login-response page
            const redirectUri = queryParams.get("redirect_uri") || "/dashboard";
            router.push(`/users/login/handle-login-response?redirect_uri=${encodeURIComponent(redirectUri)}`);
            return;
          }

          // Original flow for other responses
          showAlert(response.message, "success");
          setTimeout(() => {
            setIsRedirecting(true);
            window.location.href = "/";
          }, 3000);
        } catch (error) {
          // For other errors, continue with the normal flow
          throw error;
        }
      } else {
        if (response.message && response.message.includes("User is not confirmed")) {
          showAlert("User is not confirmed !", "danger");
          setIsLoading(false);
          router.push(
            `/users/confirm_signup?username=${encodeURIComponent(username)}`
          );
        } else {
          showAlert(response.message || "Incorrect username or password !", "danger");
          setIsLoading(false);
        }
      }
    } catch (error) {
      showAlert("Something went wrong!", "danger");
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleLogoClick = () => {
    const tenantId = encodeURIComponent(getRootTenantId());
    router.push({
      pathname: '/users/login',
      query: tenantId === process.env.NEXT_PUBLIC_ROOT_TENANT_ID
        ? { tenant_id: tenantId, ...queryParams }
        : queryParams
    });
  };

  const handleFindOrganization = () => {
    // Disable organization search only for superadmin tenant
    if (rawTenantId === process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
      return;
    }

    if (!username) {
      showAlert("Please enter an email address", "danger");
      return;
    }

    if (!verifyEmail(username)) {
      showAlert("Please enter a valid email address", "danger");
      return;
    }

    // Call fetchOrganizations to search for organizations
    fetchOrganizations(username.trim());
  };

  return (
    <LoginSignupContainer>
      <div className="bg-white rounded-lg shadow-xl p-7 max-w-md w-full z-20 text-center">
        <h1 className="project-panel-heading mb-6 typography-heading-4 font-weight-semibold">Sign in to Kavia</h1>

        {isLoadingTenant ? (
          <div className="inline-flex items-center px-3 py-1 mb-4 rounded-full bg-gradient-to-r from-primary-100 to-primary-50 border border-primary-200">
            <div className="w-2 h-2 rounded-full bg-primary-500 mr-2 animate-pulse"></div>
            <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ) : (
          <>
            {tenant_name && decryptTenantId(tenant_id) != process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID && (
              <div className={`inline-flex items-center px-3 py-1 mb-4 rounded-full ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID
                ? 'bg-gradient-to-r from-[#9D3ADF] to-[#1F6FEB] border border-[#1F6FEB]'
                : 'bg-gradient-to-r from-primary-100 to-primary-50 border border-primary-200'
                }`}>
                <span className={`w-2 h-2 rounded-full ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID ? 'bg-white' : 'bg-primary'
                  } mr-2`}></span>
                <span className={`font-weight-medium ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID ? 'text-white' : 'text-gray-800'
                  }`}>{tenant_name}</span>
              </div>
            )}
            {decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID && (
              <div className="inline-flex items-center px-3 py-1 mb-4 rounded-full bg-gradient-to-r from-primary-100 to-primary-50 border border-primary-200">
                <span className="w-2 h-2 rounded-full bg-primary mr-2"></span>
                <span className="font-weight-medium text-gray-800">Kavia Common</span>
              </div>
            )}
          </>
        )}
        <p className="text-font mb-4">
          Welcome back! Please sign in to continue
        </p>

        {/* Temporarily commenting out Google authentication */}
        {(decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID ||
          (rawTenantId && rawTenantId.startsWith('default'))) &&
          <>
            <div className="mb-6">
              <button
                type="button"
                onClick={async () => {
                  try {
                    const backendUrl = process.env.NEXT_PUBLIC_API_URL;
                    window.location.href = `${backendUrl}/auth/google?action=signup&tenant_id=${encodeURIComponent(tenant_id)}`;
                  } catch (error) {
                    showAlert("Failed to initiate Google authentication", "danger");
                  }
                }}
                className="w-full inline-flex justify-center items-center py-2.5 px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="hsl(var(--primary))"
                  />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335"
                  />
                </svg>
                <span className="typography-body-sm font-weight-medium text-gray-700">Continue with Google</span>
              </button>
            </div>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center typography-body-sm">
                <span className="px-2 bg-white text-gray-500">Or</span>
              </div>
            </div>
          </>}
       

        <form onSubmit={handleSubmit} className="flex flex-col items-center mt-6">
          {/* Email Input Group */}
          <div className="w-full relative">
            <label htmlFor="username" className="self-start mb-1 login-form block text-left">
              <strong>Email Address</strong> <span className="text-red-500">*</span>
            </label>
            <div className="relative mb-4">
              <input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Email Address"
                required
                className="w-full p-1.5 pr-12 border border-gray-300 rounded-md"
                onBlur={() => {
                  if (username && username.includes('@') && rawTenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
                    handleFindOrganization();
                  }
                  setNote(true)
                }}
                onFocus={() => setNote(false)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && username && username.includes('@') && rawTenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
                    e.preventDefault();
                    handleFindOrganization();
                  }
                }}
              />
              {/* Show search icon for all users except superadmin */}
              {rawTenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID && (
                <BootstrapTooltip title="Search for organizations" placement="top" >
                  <span>
                    <button
                      type="button"
                      onClick={handleFindOrganization}
                      disabled={isSearchingOrgs || !username || !username.includes('@')}
                      className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-md
                      ${isSearchingOrgs || !username || !username.includes('@')
                          ? 'text-gray-300'
                          : 'text-[hsl(var(--primary))] hover:text-primary-600'}`}
                    >
                      {isSearchingOrgs ? (
                        <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <Search className="w-5 h-5" />
                      )}
                    </button>
                  </span>
                </BootstrapTooltip>
              )}
            </div>
          </div>

          {/* Organization Dropdown - show for all users with multiple orgs */}
          {showOrgDropdown && (
            <div className="w-full relative mb-4 org-dropdown-container">
              <label htmlFor="organization" className="self-start mb-1 login-form block text-left">
                <strong>Select Organization</strong> <span className="text-red-500">*</span>
              </label>

              <div className="relative">
                <div
                  className="w-full border border-gray-300 rounded-md p-1.5 cursor-pointer flex justify-between items-center bg-primary-50 hover:border-primary transition-colors"
                  onClick={() => setIsOrgDropdownOpen(!isOrgDropdownOpen)}
                >
                  <span className="text-gray-1000 font-weight-medium">
                    {selectedOrg
                      ? organizations.find(org => org.id === selectedOrg)?.name === "Default"
                        ? "Kavia Common"
                        : organizations.find(org => org.id === selectedOrg)?.name || "Choose an organization"
                      : "Choose an organization"}
                  </span>
                  <svg
                    className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${isOrgDropdownOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>

                {isOrgDropdownOpen && (
                  <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                    <div className="p-1.5 border-b border-gray-200">
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search organizations..."
                          className="w-full p-1.5 pl-8 bg-white border border-gray-300 rounded-md focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary-500"
                          value={orgSearchTerm}
                          onChange={(e) => setOrgSearchTerm(e.target.value)}
                        />
                        <Search className="w-4 h-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      </div>
                    </div>
                    <div className="max-h-40 overflow-y-auto">
                      {filteredOrganizations.length > 0 ? (
                        filteredOrganizations.map((org) => (
                          <div
                            key={org.id}
                            className={`p-1.5 hover:bg-primary-50 cursor-pointer transition-colors text-left
                            ${org.id === selectedOrg ? 'bg-primary-50 text-primary-600' : 'text-gray-700'}
                            ${filteredOrganizations.length === 1 ? 'rounded-b-lg' : ''}`}
                            onClick={() => {
                              setSelectedOrg(org.id);
                              setIsOrgDropdownOpen(false);
                              handleOrgChange({ target: { value: org.id } });
                            }}
                          >
                            {org.name === "Default" ? "Kavia Common" : org.name}
                          </div>
                        ))
                      ) : (
                        <div className="p-1.5 text-gray-500 text-left">
                          No organizations found
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Password Input Group */}
          <div className="w-full">
            <label
              htmlFor="password"
              className="self-start mb-1 login-form block text-left"
            >
              <strong>Password</strong> <span className="text-red-500">*</span>
            </label>
            <div className="relative w-full mb-2">
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                required
                className="w-full p-1.5 border border-gray-300 rounded-md pr-20"
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute inset-y-0 right-0 flex items-center text-gray-600" style={{ paddingRight: '130px' }}
              >
                {showPassword ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-6 h-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-6 h-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
                    />
                  </svg>
                )}
              </button>
              <Link
                href={`/users/forgot_password?tenant_id=${encodeURIComponent(tenant_id || '')}`}
                className="absolute right-2 top-2 text-[hsl(var(--primary))] text-md"
              >
                Forgot password?
              </Link>
            </div>
          </div>

          <button
            disabled={isLoading || !verifyEmail(username) || !password || (showOrgDropdown && !selectedOrg)}
            className={`
          w-full mt-2 relative py-2 px-4 rounded-lg font-weight-medium transition-all duration-200
          ${isLoading || !verifyEmail(username) || !password || (showOrgDropdown && !selectedOrg)
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-orange-500 hover:bg-orange-600 active:bg-orange-700 cursor-pointer'}
          border-2 ${isLoading || !verifyEmail(username) || !password || (showOrgDropdown && !selectedOrg) ? 'border-gray-500' : 'border-orange-600'}
          text-white shadow-md hover:shadow-lg
        `}
          >
            <div className="flex items-center justify-center">
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <>
                  <span>Login</span>
                  <ArrowRightIcon className="w-4 h-4 ml-2" />
                </>
              )}
            </div>
          </button>
        </form>

        <div className="mt-4 border-t text-md">
          <p className="mt-3 text-gray-600">
            Don't have an account?{" "}
            <Link
              href={`/users/sign_up${tenant_id && decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID ? `?tenant_id=${encodeURIComponent(decryptTenantId(tenant_id))}` : ''}`}
              className="text-[hsl(var(--primary))]"
              prefetch={true}
            >
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </LoginSignupContainer>
  );
};

export default LoginPage;