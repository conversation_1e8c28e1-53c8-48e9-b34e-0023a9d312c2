import React, { useState, useEffect } from 'react';
import Form from '@rjsf/shadcn';
import validator from '@rjsf/validator-ajv8';
import { getManifestFormConfig, saveManifestFormData, generateManifestJson, getManifestHistory, getManifestVersion, restoreManifestVersion } from '@/utils/manifestAPI';
import { X, AlertCircle, CheckCircle, Save, FileText, Box, ChevronRight, ChevronDown, ChevronUp, Plus, Trash2, RefreshCw, Sparkles, Clock, RotateCcw, Loader2, Eye, Calendar, GitBranch, Check } from 'lucide-react';
import "src/styles/rjsf.css";

const ManifestEditor = ({ projectId, onSave, onError, onClose, isOpen = true , customButtonLabel = null, customButtonAction = null }) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [formConfig, setFormConfig] = useState(null);
  const [formData, setFormData] = useState({});
  const [error, setError] = useState(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [generateSuccess, setGenerateSuccess] = useState(false);
  const [activeSection, setActiveSection] = useState('overview');
  const [selectedContainerIndex, setSelectedContainerIndex] = useState(null);
  const [historyOpen, setHistoryOpen] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [historyError, setHistoryError] = useState(null);
  const [manifestHistory, setManifestHistory] = useState([]);
  const [selectedHistoryIndex, setSelectedHistoryIndex] = useState(null);
  const [restoringVersion, setRestoringVersion] = useState(null);
  const [viewingVersion, setViewingVersion] = useState(null);
  const [versionDetails, setVersionDetails] = useState(null);
  const [showRestoreConfirm, setShowRestoreConfirm] = useState(null);

  // Check if manifest data exists
  const hasManifestData = formData?.containers?.length > 0 || formData?.overview?.project_name;

  useEffect(() => {
    if (isOpen) {
      loadFormConfig();
    }
  }, [projectId, isOpen]);

  const loadFormConfig = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const config = await getManifestFormConfig(projectId);
      setFormConfig(config);
      setFormData(config.formData || {});
      
    } catch (err) {
      setError(`Failed to load form configuration: ${err.message}`);
      onError?.(err);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateManifest = async () => {
    try {
      setGenerating(true);
      setError(null);
      setGenerateSuccess(false);
      
      // Call generate manifest with regenerate=true to force regeneration
      const response = await generateManifestJson(projectId, true, true);
      
      if (response.success) {
        // Update form with generated data
        setFormConfig({
          jsonSchema: response.jsonSchema,
          uiSchema: response.uiSchema
        });
        setFormData(response.formData || {});
        
        setGenerateSuccess(true);
        
        // Clear success message after 3 seconds
        setTimeout(() => setGenerateSuccess(false), 3000);
        
        // If containers were generated, switch to first container
        if (response.formData?.containers?.length > 0) {
          setActiveSection('container');
          setSelectedContainerIndex(0);
        }
      } else {
        throw new Error(response.error || 'Failed to generate manifest');
      }
      
    } catch (err) {
      setError(`Failed to generate manifest: ${err.message}`);
      onError?.(err);
    } finally {
      setGenerating(false);
    }
  };

  const handleSubmit = async ({ formData: submittedData }) => {
    try {
      setSaving(true);
      setError(null);
      setSaveSuccess(false);

      const response = await saveManifestFormData(projectId, submittedData);
      
      setSaveSuccess(true);
      onSave?.(response, submittedData);
      
      // Refresh history after save
      if (historyOpen) {
        loadManifestHistory();
      }
      
      setTimeout(() => setSaveSuccess(false), 3000);
      
    } catch (err) {
      setError(`Failed to save manifest: ${err.message}`);
      onError?.(err);
    } finally {
      setSaving(false);
    }
  };

  // New function to handle save and then custom action
  const handleSaveAndCustomAction = async () => {
    try {
      setSaving(true);
      setError(null);
      setSaveSuccess(false);

      // First save the manifest
      const response = await saveManifestFormData(projectId, formData);
      
      setSaveSuccess(true);
      onSave?.(response, formData);
      
      // Then execute the custom action
      if (customButtonAction) {
        await customButtonAction();
      }
      
      setTimeout(() => setSaveSuccess(false), 3000);
      
    } catch (err) {
      setError(`Failed to save manifest: ${err.message}`);
      onError?.(err);
    } finally {
      setSaving(false);
    }
  };

  const handleChange = ({ formData: changedData }) => {
    setFormData(changedData);
  };

  const handleError = (errors) => {
    console.log('Form validation errors:', errors);
  };

  const handleClose = () => {
    if (!saving && !generating) {
      onClose?.();
    }
  };

  const handleAddContainer = () => {
    const newContainer = {
      container_name: `container_${(formData.containers?.length || 0) + 1}`,
      description: '',
      container_type: 'frontend',
      framework: 'react',
      interfaces: '',
      dependent_containers: [],
      workspace: '',
      container_root: '',
      port: '',
      container_details: {
        features: [],
        colors: {
          primary: '',
          secondary: '',
          accent: ''
        }
      }
    };

    const updatedFormData = {
      ...formData,
      containers: [...(formData.containers || []), newContainer]
    };
    
    setFormData(updatedFormData);
    setSelectedContainerIndex(updatedFormData.containers.length - 1);
    setActiveSection('container');
  };

  const handleRemoveContainer = (index) => {
    const updatedContainers = formData.containers.filter((_, i) => i !== index);
    const updatedFormData = {
      ...formData,
      containers: updatedContainers
    };
    
    setFormData(updatedFormData);
    
    // Adjust selected container if necessary
    if (selectedContainerIndex === index) {
      setSelectedContainerIndex(null);
      setActiveSection('overview');
    } else if (selectedContainerIndex > index) {
      setSelectedContainerIndex(selectedContainerIndex - 1);
    }
  };

  const renderFormSection = () => {
    if (!formConfig) return null;

    let filteredSchema = { ...formConfig.jsonSchema };
    let filteredUiSchema = { ...formConfig.uiSchema };

    if (activeSection === 'overview') {
      // Show only overview section
      filteredSchema.properties = {
        overview: formConfig.jsonSchema.properties.overview
      };
      filteredUiSchema = {
        overview: formConfig.uiSchema.overview
      };
    } else if (activeSection === 'container' && selectedContainerIndex !== null) {
      // Show only the selected container
      const containerSchema = formConfig.jsonSchema.properties.containers.items;
      
      filteredSchema = {
        type: 'object',
        properties: {
          container: containerSchema
        }
      };
      
      filteredUiSchema = {
        container: formConfig.uiSchema.containers?.items || {}
      };
      
      // Extract single container data for form
      const singleContainerData = {
        container: formData.containers?.[selectedContainerIndex] || {}
      };
      
      return (
        <Form
          schema={filteredSchema}
          uiSchema={filteredUiSchema}
          formData={singleContainerData}
          validator={validator}
          onSubmit={(data) => {
            // Update the specific container in the array
            const updatedContainers = [...(formData.containers || [])];
            updatedContainers[selectedContainerIndex] = data.formData.container;
            handleSubmit({
              formData: {
                ...formData,
                containers: updatedContainers
              }
            });
          }}
          onChange={(data) => {
            // Update the specific container in the array
            const updatedContainers = [...(formData.containers || [])];
            updatedContainers[selectedContainerIndex] = data.formData.container;
            handleChange({
              formData: {
                ...formData,
                containers: updatedContainers
              }
            });
          }}
          onError={handleError}
          showErrorList={false}
          disabled={saving || generating}
          className="professional-form single-container-form"
        >
          <button type="submit" className="hidden" />
        </Form>
      );
    }

    return (
      <Form
        schema={filteredSchema}
        uiSchema={filteredUiSchema}
        formData={formData}
        validator={validator}
        onSubmit={handleSubmit}
        onChange={handleChange}
        onError={handleError}
        showErrorList={false}
        disabled={saving || generating}
        className="professional-form"
      >
        <button type="submit" className="hidden" />
      </Form>
    );
  };

  // Fetch manifest history when sidebar section is opened
  const loadManifestHistory = async () => {
    setHistoryLoading(true);
    setHistoryError(null);
    try {
      const res = await getManifestHistory(projectId, 10);
      setManifestHistory(res.history || []);
    } catch (err) {
      setHistoryError(err.message);
    } finally {
      setHistoryLoading(false);
    }
  };

  // Restore a specific version with elegant confirmation
  const handleRestoreVersion = async (versionIdx) => {
    setShowRestoreConfirm(versionIdx);
  };

  const confirmRestoreVersion = async (versionIdx) => {
    setRestoringVersion(versionIdx);
    setShowRestoreConfirm(null);
    try {
      await restoreManifestVersion(projectId, versionIdx);
      await loadFormConfig(); // Reload form with restored data
      setSelectedHistoryIndex(null);
      setViewingVersion(null);
      setVersionDetails(null);
      // Refresh history
      await loadManifestHistory();
    } catch (err) {
      setError(`Failed to restore version: ${err.message}`);
    } finally {
      setRestoringVersion(null);
    }
  };

  // View a specific version with elegant inline display
  const handleViewVersion = async (versionIdx) => {
    if (viewingVersion === versionIdx) {
      // Close if already viewing this version
      setViewingVersion(null);
      setVersionDetails(null);
      setSelectedHistoryIndex(null);
      return;
    }

    setViewingVersion(versionIdx);
    setSelectedHistoryIndex(versionIdx);
    try {
      const res = await getManifestVersion(projectId, versionIdx);
      setVersionDetails(res.version_data);
    } catch (err) {
      setError(`Failed to load version: ${err.message}`);
      setViewingVersion(null);
      setVersionDetails(null);
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'Unknown date';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.abs(now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  useEffect(() => {
    if (historyOpen) {
      loadManifestHistory();
    }
    // eslint-disable-next-line
  }, [historyOpen, projectId]);

  if (!isOpen) return null;

  return (
    <>
      {/* Modal Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={handleClose}
      >
        {/* Modal Container */}
        <div 
          className="bg-white rounded-lg shadow-2xl w-full max-w-7xl max-h-[90vh] flex flex-col overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Fixed Header */}
          <div className="bg-gradient-to-r from-orange-500 to-orange-400 text-white px-6 py-4 flex items-center justify-between shrink-0">
            <div className="flex-1">
              <h1 className="text-xl font-semibold">Project Manifest Editor</h1>
              <p className="text-orange-100 text-sm mt-1">
                Configure your project structure and containers
              </p>
            </div>
            
            {/* Generate/Regenerate Button */}
            <button
              onClick={handleGenerateManifest}
              disabled={generating || saving || loading}
              className="mr-4 px-4 py-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center gap-2 text-sm font-medium border border-white/20"
              title={hasManifestData ? "Regenerate manifest from codebase" : "Generate manifest from codebase"}
            >
              {generating ? (
                <>
                  <RefreshCw size={16} className="animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  {hasManifestData ? <RefreshCw size={16} /> : <Sparkles size={16} />}
                  {hasManifestData ? 'Regenerate Manifest' : 'Generate Manifest'}
                </>
              )}
            </button>
            
            <button
              onClick={handleClose}
              disabled={saving || generating}
              className="p-2 hover:bg-white/10 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Close modal"
            >
              <X size={20} />
            </button>
          </div>

          {/* Alert Messages */}
          {(error || saveSuccess || generateSuccess) && (
            <div className="px-6 pt-4 shrink-0">
              {error && (
                <div className="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 mb-2">
                  <AlertCircle size={18} className="shrink-0" />
                  <span className="flex-1 text-sm">{error}</span>
                  <button 
                    onClick={() => setError(null)} 
                    className="p-1 hover:bg-red-100 rounded transition-colors"
                    aria-label="Close error"
                  >
                    <X size={14} />
                  </button>
                </div>
              )}

              {saveSuccess && (
                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-md text-green-700 mb-2">
                  <CheckCircle size={18} className="shrink-0" />
                  <span className="text-sm">Manifest saved successfully!</span>
                </div>
              )}

              {generateSuccess && (
                <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-700 mb-2">
                  <CheckCircle size={18} className="shrink-0" />
                  <span className="text-sm">Manifest {hasManifestData ? 'regenerated' : 'generated'} successfully from codebase!</span>
                </div>
              )}
            </div>
          )}

          {/* Main Content Area with Sidebar */}
          <div className="flex flex-1 overflow-hidden">
            {/* Side Navigation */}
            <div className="w-80 bg-gray-50 border-r border-gray-200 flex-shrink-0 flex flex-col">
              <div className="flex-1 overflow-y-auto p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Navigation</h3>
                <nav className="space-y-1">
                  {/* Overview Section */}
                  <button
                    onClick={() => {
                      setActiveSection('overview');
                      setSelectedContainerIndex(null);
                    }}
                    className={`w-full flex items-center px-3 py-3 rounded-lg text-left transition-all duration-200 group ${
                      activeSection === 'overview'
                        ? 'bg-orange-50 text-orange-700 border border-orange-200' 
                        : 'text-gray-600 hover:bg-white hover:text-gray-900 hover:shadow-sm'
                    }`}
                  >
                    <FileText 
                      size={18} 
                      className={`mr-3 ${activeSection === 'overview' ? 'text-orange-500' : 'text-gray-400 group-hover:text-gray-500'}`} 
                    />
                    <div className="flex-1">
                      <div className="font-medium text-sm">Project Overview</div>
                      <div className="text-xs text-gray-500 mt-0.5">Basic project information</div>
                    </div>
                    <ChevronRight 
                      size={16} 
                      className={`transition-transform ${activeSection === 'overview' ? 'rotate-90 text-orange-500' : 'text-gray-300'}`} 
                    />
                  </button>

                  {/* Containers Header with Add Button */}
                  <div className="mt-4 mb-2 flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-700">Containers</h4>
                    <button
                      onClick={handleAddContainer}
                      disabled={generating}
                      className="p-1.5 text-gray-500 hover:text-orange-600 hover:bg-orange-50 rounded-md transition-colors disabled:opacity-50"
                      title="Add Container"
                    >
                      <Plus size={16} />
                    </button>
                  </div>

                  {/* Individual Container Items */}
                  {formData.containers?.map((container, index) => (
                    <div
                      key={index}
                      className={`w-full flex items-center rounded-lg transition-all duration-200 group ${
                        activeSection === 'container' && selectedContainerIndex === index
                          ? 'bg-orange-50 text-orange-700 border border-orange-200' 
                          : 'text-gray-600 hover:bg-white hover:text-gray-900 hover:shadow-sm'
                      }`}
                    >
                      <button
                        onClick={() => {
                          setActiveSection('container');
                          setSelectedContainerIndex(index);
                        }}
                        className="flex-1 flex items-center px-3 py-3 text-left"
                      >
                        <Box 
                          size={18} 
                          className={`mr-3 ${
                            activeSection === 'container' && selectedContainerIndex === index 
                              ? 'text-orange-500' 
                              : 'text-gray-400 group-hover:text-gray-500'
                          }`} 
                        />
                        <div className="flex-1">
                          <div className="font-medium text-sm">
                            {container.container_name || `Container ${index + 1}`}
                          </div>
                          <div className="text-xs text-gray-500 mt-0.5">
                            {container.container_type && container.framework 
                              ? `${container.container_type} - ${container.framework}`
                              : container.container_type || 'Not configured'}
                          </div>
                        </div>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (window.confirm(`Remove container "${container.container_name || `Container ${index + 1}`}"?`)) {
                            handleRemoveContainer(index);
                          }
                        }}
                        disabled={generating}
                        className="p-1.5 mr-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors opacity-0 group-hover:opacity-100 disabled:opacity-0"
                        title="Remove Container"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                  ))}

                  {(!formData.containers || formData.containers.length === 0) && (
                    <div className="text-center py-4 text-gray-500 text-sm">
                      No containers yet.
                      <button
                        onClick={handleAddContainer}
                        disabled={generating}
                        className="block mx-auto mt-2 text-orange-600 hover:text-orange-700 font-medium disabled:opacity-50"
                      >
                        Add your first container
                      </button>
                      <div className="mt-3 text-xs">
                        or
                      </div>
                      <button
                        onClick={handleGenerateManifest}
                        disabled={generating}
                        className="mt-2 text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50 inline-flex items-center gap-1"
                      >
                        <Sparkles size={14} />
                        Generate from codebase
                      </button>
                    </div>
                  )}
                </nav>
              </div>

              {/* Manifest History Section - Fixed at bottom */}
              <div className="border-t border-gray-200 bg-white">
                <button
                  className="w-full flex items-center px-4 py-3 text-left transition-all duration-200 group text-gray-700 hover:bg-gray-50 font-medium"
                  onClick={() => setHistoryOpen((v) => !v)}
                >
                  <Clock size={18} className="mr-3 text-gray-400 group-hover:text-gray-600" />
                  <span className="flex-1">Manifest History</span>
                  {historyOpen ? (
                    <ChevronUp size={16} className="text-gray-400" />
                  ) : (
                    <ChevronDown size={16} className="text-gray-400" />
                  )}
                </button>

                {historyOpen && (
                  <div className="border-t border-gray-100 bg-gray-50 max-h-80 overflow-y-auto">
                    {historyLoading ? (
                      <div className="flex items-center justify-center gap-2 text-gray-500 text-sm py-6">
                        <Loader2 size={16} className="animate-spin" />
                        Loading history...
                      </div>
                    ) : historyError ? (
                      <div className="text-red-500 text-sm p-4 text-center">
                        <AlertCircle size={16} className="mx-auto mb-2" />
                        {historyError}
                      </div>
                    ) : manifestHistory.length === 0 ? (
                      <div className="text-gray-400 text-sm py-6 text-center">
                        <Calendar size={16} className="mx-auto mb-2" />
                        No history available
                      </div>
                    ) : (
                      <div className="p-2 space-y-1">
                        {manifestHistory.map((entry, idx) => (
                          <div
                            key={idx}
                            className={`group relative rounded-lg border transition-all duration-200 ${
                              selectedHistoryIndex === idx 
                                ? 'bg-blue-50 border-blue-200 shadow-sm' 
                                : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                            }`}
                          >
                            <div className="p-3">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <GitBranch size={14} className="text-gray-400" />
                                  <span className="text-xs font-medium text-gray-700">
                                    Version {idx + 1}
                                  </span>
                                  {idx === 0 && (
                                    <span className="px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                                      Latest
                                    </span>
                                  )}
                                </div>
                                <span className="text-xs text-gray-500">
                                  {formatDate(entry.timestamp)}
                                </span>
                              </div>
                              
                              <div className="text-xs text-gray-600 mb-3">
                                {entry.manifest?.containers?.length || 0} containers
                                {entry.manifest?.overview?.project_name && (
                                  <span className="ml-2">• {entry.manifest.overview.project_name}</span>
                                )}
                              </div>

                              <div className="flex items-center gap-2">
                                <button
                                  className={`flex-1 px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                    viewingVersion === idx
                                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                  }`}
                                  onClick={() => handleViewVersion(idx)}
                                  title={viewingVersion === idx ? "Hide details" : "View details"}
                                >
                                  <Eye size={14} className="inline mr-1" />
                                  {viewingVersion === idx ? 'Hide' : 'View'}
                                </button>
                                <button
                                  className="px-3 py-1.5 rounded-md text-xs font-medium bg-orange-100 text-orange-700 hover:bg-orange-200 transition-colors disabled:opacity-50"
                                  onClick={() => handleRestoreVersion(idx)}
                                  disabled={restoringVersion === idx}
                                  title="Restore this version"
                                >
                                  {restoringVersion === idx ? (
                                    <Loader2 size={14} className="animate-spin" />
                                  ) : (
                                    <RotateCcw size={14} />
                                  )}
                                </button>
                              </div>
                            </div>

                            {/* Version Details Panel */}
                            {viewingVersion === idx && versionDetails && (
                              <div className="border-t border-gray-200 bg-gray-50 p-3">
                                <div className="text-xs font-medium text-gray-700 mb-2">Version Details:</div>
                                <div className="bg-white border border-gray-200 rounded p-2 max-h-32 overflow-y-auto">
                                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                                    {JSON.stringify(versionDetails.manifest, null, 2)}
                                  </pre>
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Form Content Area */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {loading ? (
                <div className="flex-1 flex flex-col items-center justify-center py-16">
                  <div className="w-8 h-8 border-2 border-gray-200 border-t-orange-500 rounded-full animate-spin mb-4"></div>
                  <p className="text-gray-600">Loading manifest configuration...</p>
                </div>
              ) : generating ? (
                <div className="flex-1 flex flex-col items-center justify-center py-16">
                  <div className="w-8 h-8 border-2 border-gray-200 border-t-orange-500 rounded-full animate-spin mb-4"></div>
                  <p className="text-gray-600">Analyzing your codebase...</p>
                  <p className="text-sm text-gray-500 mt-2">Generating manifest configuration</p>
                </div>
              ) : error && !formConfig ? (
                <div className="flex-1 flex flex-col items-center justify-center py-16 text-center px-6">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <AlertCircle size={32} className="text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">Failed to Load Form</h3>
                  <p className="text-gray-600 mb-6 max-w-md">{error}</p>
                  <div className="flex gap-3">
                    <button 
                      onClick={loadFormConfig} 
                      className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-medium transition-colors"
                    >
                      Try Again
                    </button>
                    <button 
                      onClick={handleGenerateManifest} 
                      className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md font-medium transition-colors inline-flex items-center gap-2"
                    >
                      <Sparkles size={16} />
                      Generate New
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex-1 overflow-y-auto p-6">
                  <div className="max-w-4xl">
                    {/* Section Header */}
                    <div className="mb-6">
                      <h2 className="text-lg font-semibold text-gray-900 mb-1">
                        {activeSection === 'overview' 
                          ? 'Project Overview'
                          : selectedContainerIndex !== null && formData.containers?.[selectedContainerIndex]
                            ? formData.containers[selectedContainerIndex].container_name || `Container ${selectedContainerIndex + 1}`
                            : 'Select a Container'}
                      </h2>
                      <p className="text-sm text-gray-600">
                        {activeSection === 'overview' 
                          ? 'Define your project\'s basic information'
                          : selectedContainerIndex !== null
                            ? 'Configure container settings and properties'
                            : 'Choose a container from the sidebar to edit'}
                      </p>
                    </div>

                    {/* Form Content */}
                    {(activeSection === 'overview' || (activeSection === 'container' && selectedContainerIndex !== null)) && (
                      <div className="bg-white rounded-lg border border-gray-200 p-6">
                        {renderFormSection()}
                      </div>
                    )}

                    {activeSection === 'container' && selectedContainerIndex === null && (
                      <div className="bg-gray-50 rounded-lg border border-gray-200 p-8 text-center">
                        <Box size={48} className="mx-auto text-gray-300 mb-4" />
                        <p className="text-gray-600">No container selected</p>
                        <p className="text-sm text-gray-500 mt-2">Choose a container from the sidebar or add a new one</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Fixed Footer */}
          {formConfig && (
            <div className="border-t border-gray-200 px-6 py-4 bg-gray-50 shrink-0">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  {activeSection === 'overview' 
                    ? 'Project Overview'
                    : selectedContainerIndex !== null 
                      ? `Editing: ${formData.containers?.[selectedContainerIndex]?.container_name || `Container ${selectedContainerIndex + 1}`}`
                      : 'No container selected'}
                  {!hasManifestData && (
                    <span className="ml-2 text-orange-600">
                      (No manifest data - click "Generate Manifest" to analyze codebase)
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={handleClose}
                    disabled={saving || generating}
                    className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      const form = document.querySelector('.professional-form form, .single-container-form form');
                      if (form) {
                        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                        form.dispatchEvent(submitEvent);
                      }
                      // Trigger the saveManifest function after form submission
                      handleSubmit({ formData });
                    }}
                    disabled={saving || generating || !hasManifestData}
                    className="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md font-medium transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center gap-2"
                  >
                    {saving ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                        Save Manifest
                      </>
                    )}
                  </button>

                  {customButtonLabel && customButtonAction && (
                    <button
                      type="button"
                      onClick={handleSaveAndCustomAction}
                      disabled={saving || generating || !hasManifestData}
                      className="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md font-medium transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center gap-2"
                    >
                      {saving ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                          Processing...
                        </>
                      ) : (
                        customButtonLabel
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Elegant Restore Confirmation Modal */}
      {showRestoreConfirm !== null && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                <RotateCcw size={24} className="text-orange-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Restore Version</h3>
                <p className="text-sm text-gray-600">Are you sure you want to restore this manifest version?</p>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3 mb-6">
              <div className="text-sm text-gray-700">
                <strong>Version:</strong> {showRestoreConfirm + 1}
              </div>
              <div className="text-sm text-gray-700">
                <strong>Date:</strong> {formatDate(manifestHistory[showRestoreConfirm]?.timestamp)}
              </div>
              <div className="text-sm text-gray-700">
                <strong>Containers:</strong> {manifestHistory[showRestoreConfirm]?.manifest?.containers?.length || 0}
              </div>
            </div>

            <div className="text-sm text-gray-600 mb-6">
              This will overwrite your current manifest. Your current version will be automatically saved to history.
            </div>

            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowRestoreConfirm(null)}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => confirmRestoreVersion(showRestoreConfirm)}
                className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md font-medium transition-colors inline-flex items-center gap-2"
              >
                <Check size={16} />
                Restore Version
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ManifestEditor;