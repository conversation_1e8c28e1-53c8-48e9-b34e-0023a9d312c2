import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X, Rocket, Terminal, Package, Plus, Trash2, Save, ChevronDown, Folder, RefreshCw, Check, History } from 'lucide-react';
import Drawer from '@/components/Drawer';
import { useDeployment } from '@/components/Context/DeploymentContext';
import { listDeployments } from '@/utils/deploymentApi';
import { useParams, useSearchParams } from 'next/navigation';

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url: string): string => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

// Framework logo components
const ReactLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" className="text-[#61DAFB]">
    <circle cx="12" cy="12" r="2.139" fill="currentColor" />
    <path stroke="currentColor" strokeWidth="0.6" d="M12 14.880c-5.03 0-9.268-1.493-9.268-3.315 0-1.823 4.238-3.316 9.268-3.316 5.03 0 9.268 1.493 9.268 3.316 0 1.822-4.238 3.315-9.268 3.315Z" />
    <path stroke="currentColor" strokeWidth="0.6" d="m8.627 12.811.205.356c1.707 2.955 3.56 4.642 4.988 4.025 1.43-.618 1.776-3.153.86-6.302l-.092-.315m-5.755 2.236-.206-.356C7.92 9.5 8.015 6.9 9.443 6.283c1.43-.618 3.513.843 5.22 3.798l.091.158" />
  </svg>
);

const AngularLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 2.5L2.5 6l1.5 13L12 22l8-3 1.5-13L12 2.5z" fill="#DD0031" />
    <path d="M12 2.5v19.5l8-3 1.5-13L12 2.5z" fill="#C3002F" />
    <path d="M12 5l-7 15h2.6l1.4-3.5h6l1.4 3.5H18L12 5zm0 4l2.3 5.5H9.7L12 9z" fill="white" />
  </svg>
);

const NextLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path fill="black" d="M11.572 0c-.176 0-.31.001-.358.007a19.76 19.76 0 0 0-.364.033C7.443.346 4.25 2.185 2.228 5.012a11.875 11.875 0 0 0-2.119 5.243c-.096.659-.108.854-.108 1.747s.012 1.089.108 1.748c.652 4.506 3.86 8.292 8.209 9.695.779.25 1.6.422 2.534.525.363.04 1.935.04 2.299 0 1.611-.178 2.977-.577 4.323-1.264.207-.106.247-.134.219-.158-.02-.013-.9-1.193-1.957-2.62l-1.919-2.592-2.404-3.558a338.739 338.739 0 0 0-2.422-3.556c-.009-.002-.018 1.579-.023 3.51-.007 3.38-.01 3.515-.052 3.595a.426.426 0 0 1-.206.214c-.075.037-.14.044-.495.044H7.81l-.108-.068a.438.438 0 0 1-.157-.172l-.05-.106.006-4.703.007-4.705.072-.092a.645.645 0 0 1 .174-.143c.096-.047.134-.051.5-.051.478 0 .558.018.682.154.035.038 1.337 1.999 2.895 4.361a10760.433 10760.433 0 0 0 4.735 7.17l1.9 2.879.096-.063a12.317 12.317 0 0 0 2.466-2.163 11.944 11.944 0 0 0 2.824-6.134c.096-.66.108-.854.108-1.748 0-.893-.012-1.088-.108-1.747-.652-4.506-3.859-8.292-8.208-9.695a12.597 12.597 0 0 0-2.499-.523A33.119 33.119 0 0 0 11.573 0zm4.069 7.217c.347 0 .408.005.486.047a.473.473 0 0 1 .237.277c.018.06.023 1.365.018 4.304l-.006 4.218-.744-1.14-.746-1.14v-3.066c0-1.983.01-3.097.023-3.15a.478.478 0 0 1 .233-.296c.096-.05.13-.054.5-.054z" />
  </svg>
);

const ViteLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12.5 4.3L3.5 7.8l1.7 14.3 7.4 3.9 7.2-4 1.7-14.3-9-3.4z" fill="#646CFF" />
    <path d="M16.3 5L12 9.8 7.7 5h8.6z" fill="#FFDD35" />
    <path d="M16.9 18.4c.9-.9 1.4-2.1 1.4-3.4 0-2.6-2.1-4.7-4.7-4.7-1.3 0-2.5.5-3.4 1.4l6.7 6.7z" fill="white" />
    <path d="M7.1 18.4c-.9-.9-1.4-2.1-1.4-3.4 0-2.6 2.1-4.7 4.7-4.7 1.3 0 2.5.5 3.4 1.4l-6.7 6.7z" fill="white" />
  </svg>
);

const VueLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 4.5L14.5 9h-5L12 4.5z" fill="#41B883" />
    <path d="M12 4.5L9.5 9h-4L12 4.5z" fill="#41B883" />
    <path d="M2 9h3.5L12 22.5 20.5 9H24L12 29 2 9z" fill="#41B883" />
    <path d="M2 9l10 20L22 9h-4.5L12 19 6.5 9H2z" fill="#35495E" />
  </svg>
);

const NuxtLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 2L22 7v10l-10 5L2 17V7l10-5z" fill="#00DC82" />
    <path d="M12 7v10l8-4V9l-8-2z" fill="#00C58E" />
    <path d="M4 9v4l8 4V7L4 9z" fill="#108775" />
  </svg>
);

// Backend framework logo components
const FastAPILogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 2L14.5 9h7L17 13.5l2.5 7L12 17l-7.5 3.5L7 13.5 2.5 9h7L12 2z" fill="#009485" />
    <circle cx="12" cy="12" r="3" fill="white" />
  </svg>
);

const NodeLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M12 2L22 7v10l-10 5L2 17V7l10-5z" fill="#68A063" />
    <path d="M12 7v10l8-4V9l-8-2z" fill="#8CC84B" />
  </svg>
);

const DjangoLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <rect x="4" y="2" width="4" height="20" rx="2" fill="#092E20" />
    <rect x="10" y="6" width="4" height="16" rx="2" fill="#092E20" />
    <rect x="16" y="4" width="4" height="18" rx="2" fill="#092E20" />
  </svg>
);

const ExpressLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M24 18.588a1.529 1.529 0 01-1.895-.72l-3.45-4.771-.5-.667-4.003 5.444a1.466 1.466 0 01-1.802.708l5.158-6.92-4.798-6.251a1.595 1.595 0 011.9-.666L17.227 10.5l.582.775 3.581-4.96a1.574 1.574 0 011.892.664l-4.645 6.4 4.803 6.2z" fill="#68A063" />
  </svg>
);

const FlaskLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none">
    <path d="M9 2h6v6l6 12H3l6-12V2z" fill="#000" />
    <path d="M9 8h6l5 10H4l5-10z" fill="#fff" />
  </svg>
);

interface DeploymentInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  selectedContainer?: any;
  onOpenDeploymentHistory?: () => void;
}

interface EnvVariable {
  key: string;
  value: string;
  id: string;
}

interface PresetConfig {
  name: string;
  command: string;
  installPackages: boolean;
  envVariables: EnvVariable[];
  deploymentPath: string;
}

interface DirContent {
  name: string;
  type: 'file' | 'folder';
}

// Interface for deployment data
interface Deployment {
  _id: string;
  deployment_id: string;
  project_id: string;
  task_id: string;
  app_id: string;
  branch_name: string;
  app_url: string;
  artifact_path: string;
  job_id: string;
  status: string;
  message: string;
  command: string;
  root_path: string;
  build_path: string;
  created_at: string;
  updated_at: string;
  branch?: string;
}

const PLATFORM_PRESETS: Record<string, PresetConfig> = {
  netlify: {
    name: 'Netlify',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'NETLIFY', value: 'true', id: 'preset-2' }
    ],
    deploymentPath: 'dist'
  },
  vercel: {
    name: 'Vercel',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'VERCEL', value: 'true', id: 'preset-2' }
    ],
    deploymentPath: '/'
  },
  githubPages: {
    name: 'GitHub Pages',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'PUBLIC_URL', value: '/${repository_name}', id: 'preset-2' }
    ],
    deploymentPath: 'build'
  },
  cloudflare: {
    name: 'Cloudflare Pages',
    command: 'npm run build',
    installPackages: true,
    envVariables: [
      { key: 'NODE_ENV', value: 'production', id: 'preset-1' },
      { key: 'CF_PAGES', value: 'true', id: 'preset-2' }
    ],
    deploymentPath: 'dist'
  }
};

// Framework-specific build commands
const FRAMEWORK_COMMANDS: Record<string, {command: string, description: string, type: 'frontend' | 'backend'}> = {
  react: {
    command: 'npm run build',
    description: 'Create React App',
    type: 'frontend'
  },
  angular: {
    command: 'ng build',
    description: 'Angular CLI',
    type: 'frontend'
  },
  nextjs: {
    command: 'npm run build',
    description: 'Next.js Build',
    type: 'frontend'
  },
  nuxt: {
    command: 'npm run generate',
    description: 'Nuxt.js',
    type: 'frontend'
  },
  vite: {
    command: 'vite build',
    description: 'Vite.js',
    type: 'frontend'
  },
  vue: {
    command: 'npm run build',
    description: 'Vue CLI',
    type: 'frontend'
  },
  fastapi: {
    command: 'uvicorn src.api.main:app --host 0.0.0.0 --port 8000',
    description: 'FastAPI',
    type: 'backend'
  },
  node: {
    command: 'npm start',
    description: 'Node.js',
    type: 'backend'
  },
  express: {
    command: 'node server.js',
    description: 'Express.js',
    type: 'backend'
  },
  django: {
    command: 'python manage.py runserver 0.0.0.0:8000',
    description: 'Django',
    type: 'backend'
  },
  flask: {
    command: 'flask run --host=0.0.0.0 --port=5000',
    description: 'Flask',
    type: 'backend'
  }
};

// Framework logo map (update to include backend logos)
const FRAMEWORK_LOGOS: Record<string, React.ReactNode> = {
  react: <ReactLogo />,
  angular: <AngularLogo />,
  nextjs: <NextLogo />,
  nuxt: <NuxtLogo />,
  vite: <ViteLogo />,
  vue: <VueLogo />,
  fastapi: <FastAPILogo />,
  node: <NodeLogo />,
  express: <ExpressLogo />,
  django: <DjangoLogo />,
  flask: <FlaskLogo />
};

// Helper function to extract project name from root_path
const extractProjectNameFromPath = (rootPath: string): string => {
  if (!rootPath) return '';

  // Handle absolute paths by getting just the repo/project name
  if (rootPath.includes('/home/') || rootPath.includes('/Users/')) {
    // Try to extract the last meaningful directory name
    const segments = rootPath.split('/').filter(Boolean);
    if (segments.length > 0) {
      // Skip common path segments like 'home', 'workspace', etc.
      for (let i = segments.length - 1; i >= 0; i--) {
        const segment = segments[i];
        if (!['home', 'workspace', 'repos', 'users', 'src'].includes(segment.toLowerCase())) {
          return segment;
        }
      }
    }
  }

  // Split the path and get the last non-empty segment
  const segments = rootPath.split('/').filter(Boolean);
  if (segments.length > 0) {
    return segments[segments.length - 1];
  }
  return '';
};

const DeploymentInterface: React.FC<DeploymentInterfaceProps> = ({
  isOpen,
  onClose,
  selectedContainer,
  onOpenDeploymentHistory,
}) => {

  const {
    // Repository data
    repositories,
    currentRepository,
    setCurrentRepository,
    deploymentPath,
    setDeploymentPath,
    isLoadingRepo,
    fetchRepositories,

    // Deployment status
    isDeploying,
    setIsDeploying,

    // Deployment configuration
    command,
    setCommand,
    installPackages,
    setInstallPackages,
    envVariables,
    setEnvVariables,

    // Directory browsing
    dirContents,
    currentPath,
    isLoadingDir,

    // Manifest data
    manifest,

    // WebSocket
    wsConnection,
    taskId,

    // Functions
    launchDeployment,
    fetchDirectoryContents,
    fetchManifest
  } = useDeployment();

  const { projectId } = useParams();
  const searchParams = useSearchParams();
  
  // Get container type from query parameters
  const containerType: 'frontend' | 'backend' = searchParams.get('type') === 'backend' ? 'backend' : 'frontend'; // Default to frontend
  const isFrontend = containerType === 'frontend';
  const isBackend = containerType === 'backend';
  
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [isLoadingDeployments, setIsLoadingDeployments] = useState<boolean>(false);
  const [selectedDeployment, setSelectedDeployment] = useState<Deployment | null>(null);
  const [showDeploymentsDropdown, setShowDeploymentsDropdown] = useState<boolean>(false);
  const [hasTriedFetching, setHasTriedFetching] = useState<boolean>(false);
  const [fullCommand, setFullCommand] = useState('');
  const [showPlatformDropdown, setShowPlatformDropdown] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [savedConfigs, setSavedConfigs] = useState<PresetConfig[]>([]);
  const [showSavedConfigsDropdown, setShowSavedConfigsDropdown] = useState(false);
  const [showDirBrowser, setShowDirBrowser] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [showLaunchAnimation, setShowLaunchAnimation] = useState(false);
  const [deploymentError, setDeploymentError] = useState<string | null>(null);

  const [showFrameworkDropdown, setShowFrameworkDropdown] = useState(false);
  const [selectedFramework, setSelectedFramework] = useState<string | null>(null);
  const [isCreateNewApp, setIsCreateNewApp] = useState(false);
  const [buildCommand, setBuildCommand] = useState('');
  const [hasManifestLoaded, setHasManifestLoaded] = useState(false);

  // Add at the top, after other useState declarations
  // Removed deployment loading states and messages
  const [cleanBuild, setCleanBuild] = useState(false);
  const [isConfigurationSaved, setIsConfigurationSaved] = useState(false);

  // Add loading state to prevent multiple concurrent fetches
  const [isInitializing, setIsInitializing] = useState(false);
  const [hasDirectoryFetched, setHasDirectoryFetched] = useState(false);

  // Track if user has manually edited the command
  const [isCommandManuallyEdited, setIsCommandManuallyEdited] = useState(false);
  
  // Remove progressive loading states - show content immediately
  
  // Preload data when modal is about to open
  useEffect(() => {
    if (isOpen && !isInitializing) {
      // Preload repositories if not already loaded
      if (repositories.length === 0) {
        fetchRepositories();
      }
      
      // Preload manifest if we have repository but no manifest
      if (currentRepository && !manifest) {
        fetchManifest();
      }
    }
  }, [isOpen, isInitializing, repositories.length, currentRepository, manifest, fetchRepositories, fetchManifest]);

  // Add state for save configuration animation
  const [isSavingConfig, setIsSavingConfig] = useState(false);
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);
  
  // Add ref for scrolling
  const contentRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when deployment error appears
  useEffect(() => {
    if (deploymentError && contentRef.current) {
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollTop = contentRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [deploymentError]);

  // Note: Directory loading timeout is handled in DeploymentContext.js
  // This prevents infinite loading states

  // Get the current container configuration based on type
  const currentContainerConfig = containerType === 'backend' 
    ? manifest?.backend 
    : manifest?.frontend;

  // Debug logging
  useEffect(() => {
    if (manifest && containerType === 'backend') {
      console.log('Backend manifest config:', manifest.backend);
      console.log('Current container config:', currentContainerConfig);
    }
  }, [manifest, containerType, currentContainerConfig]);

  // Update deployment type based on container type
  const getDeploymentType = () => {
    if (containerType === 'backend') {
      return currentContainerConfig?.framework === 'fastapi' ? 'fastapi_app' : 'backend_app';
    }
    return 'react_app'; // Default for frontend
  };

  // Filter frameworks based on container type
  const getFrameworksForType = () => {
    return Object.entries(FRAMEWORK_COMMANDS).filter(([_, config]) => 
      config.type === containerType
    );
  };

  // Update package installation logic based on container type
  const getPackageInstallCommand = () => {
    if (containerType === 'backend') {
      // For Python-based backends, use pip install
      if (currentContainerConfig?.framework === 'fastapi' || 
          currentContainerConfig?.framework === 'django' || 
          currentContainerConfig?.framework === 'flask') {
        return 'pip install -r requirements.txt';
      }
      // For Node.js-based backends
      return 'npm install';
    }
    // Default to npm install for frontend
    return 'npm install';
  };

  // Update full command generation based on container type
  useEffect(() => {
    let cmd = '';

    // Add clean command if enabled
    if (cleanBuild) {
      cmd = 'rm -rf build dist out .next .nuxt .cache .vite .angular .svelte-kit node_modules/.cache && ';
    }

    // Only add package installation for frontend containers if command doesn't already include it
    if (containerType === 'frontend' && installPackages && !command.includes('npm install')) {
      cmd += `${getPackageInstallCommand()} && `;
    }

    cmd += command;
    setFullCommand(cmd);
  }, [installPackages, command, containerType, currentContainerConfig, cleanBuild]);

  // Helper function to get the base project directory from manifest
  const getBaseProjectDirectory = useCallback(() => {
    let baseDir = currentRepository?.name;
    
    if (currentContainerConfig?.base_path) {
      // Extract base project directory from base_path
      // e.g., "webtictactoe-61587-c2ad2f48/tic_tac_toe_frontend" -> "webtictactoe-61587-c2ad2f48"
      const pathParts = currentContainerConfig.base_path.split('/');
      if (pathParts.length > 1) {
        baseDir = pathParts[0];
      }
    }
    
    return baseDir;
  }, [currentRepository?.name, currentContainerConfig?.base_path]);

  // SIMPLIFIED MODAL INITIALIZATION EFFECT
  useEffect(() => {
    if (!isOpen) {
      // Reset all states when modal closes
      setHasTriedFetching(false);
      setSelectedFolder('');
      setIsCreateNewApp(false);
      setHasManifestLoaded(false);
      setDeploymentError(null);
      setIsInitializing(false);
      setIsConfigurationSaved(false);
      setHasDirectoryFetched(false);
      setIsSavingConfig(false);
      setShowSaveSuccess(false);
      setSelectedDeployment(null);
      setShowDeploymentsDropdown(false);
      setIsCommandManuallyEdited(false);
      return;
    }

    // Reset deployment selection when modal opens to ensure clean state
    setSelectedDeployment(null);
    setShowDeploymentsDropdown(false);

    // Set default isCreateNewApp based on container type immediately
    if (isFrontend) {
      setIsCreateNewApp(false);
    } else if (isBackend) {
      setIsCreateNewApp(true);
    }
    
    // Fetch repositories if needed (non-blocking)
    if (repositories.length === 0) {
      fetchRepositories();
    }
    
    // Always fetch manifest when modal opens for backend
    if (isBackend && currentRepository) {
      fetchManifest();
    }
    
    // For frontend, fetch manifest if we have repository but no manifest
    if (isFrontend && currentRepository && !manifest) {
      fetchManifest();
    }
    
    // Mark as initialized immediately
    setIsInitializing(false);
  }, [isOpen, repositories.length, fetchRepositories, isFrontend, isBackend, currentRepository, manifest, fetchManifest]);

  // Reset selectedDeployment when container type changes to prevent ID duplication
  useEffect(() => {
    if (isOpen && selectedDeployment) {
      // Check if the selected deployment is for the wrong type
      const selectedDeploymentType = selectedDeployment.deployment_id.startsWith('frontend_') ? 'frontend' : 
                                   selectedDeployment.deployment_id.startsWith('backend_') ? 'backend' : null;
      
      if (selectedDeploymentType && selectedDeploymentType !== containerType) {
        // Wrong type, reset selection
        setSelectedDeployment(null);
        setShowDeploymentsDropdown(false);
      }
    }
  }, [containerType, isOpen, selectedDeployment]);

  // Update configuration based on manifest - ONLY WHEN MANIFEST CHANGES
  useEffect(() => {
    if (!currentContainerConfig || isInitializing) return;

    // Set framework
    if (currentContainerConfig.framework && !selectedFramework) {
      setSelectedFramework(currentContainerConfig.framework);
    }

    // Set commands based on container type
    if (containerType === 'backend') {
      // Backend configuration
      if (currentContainerConfig.buildCommand) {
        setBuildCommand(currentContainerConfig.buildCommand);
      } else {
        // Default build command based on framework
        if (currentContainerConfig.framework === 'fastapi' || 
            currentContainerConfig.framework === 'django' || 
            currentContainerConfig.framework === 'flask') {
          setBuildCommand('pip install -r requirements.txt');
        } else {
          setBuildCommand('npm install');
        }
      }
      
      // Only set start command if not manually edited
      if (!isCommandManuallyEdited) {
        if (currentContainerConfig.startCommand) {
          let startCommand = currentContainerConfig.startCommand;
          startCommand = startCommand.replace(/<host>/g, '0.0.0.0');
          if (currentContainerConfig.ports) {
            startCommand = startCommand.replace(/<port>/g, currentContainerConfig.ports.toString());
          }
          setCommand(startCommand);
        } else {
          // Default start command based on framework
          if (currentContainerConfig.framework === 'fastapi') {
            setCommand('uvicorn main:app --host 0.0.0.0 --port 8000');
          } else if (currentContainerConfig.framework === 'django') {
            setCommand('python manage.py runserver 0.0.0.0:8000');
          } else if (currentContainerConfig.framework === 'flask') {
            setCommand('flask run --host=0.0.0.0 --port=5000');
          } else {
            setCommand('npm start');
          }
        }
      }
    } else {
      // Frontend configuration - only set if not manually edited
      if (!isCommandManuallyEdited) {
        // Prioritize user's framework selection over manifest framework
        const frameworkToUse = selectedFramework || currentContainerConfig.framework;
        
        if (frameworkToUse && FRAMEWORK_COMMANDS[frameworkToUse]) {
          setCommand(FRAMEWORK_COMMANDS[frameworkToUse].command);
        } else if (currentContainerConfig.buildCommand) {
          setCommand(currentContainerConfig.buildCommand);
        } else {
          setCommand('npm run build');
        }
      }
    }

    // Set environment variables
    if (currentContainerConfig.env && typeof currentContainerConfig.env === 'object') {
      const envEntries = Object.entries(currentContainerConfig.env);
      const newEnvVars = envEntries.map(([key, value], index) => ({
        key,
        value: String(value || ''),
        id: `manifest-${index}-${Date.now()}`
      }));
      setEnvVariables(newEnvVars);
    } else {
      setEnvVariables([]);
    }

    // Set deployment path
    if (currentContainerConfig.base_path) {
      const normalizedPath = currentContainerConfig.base_path.startsWith('/') 
        ? currentContainerConfig.base_path 
        : `/${currentContainerConfig.base_path}`;
      setDeploymentPath(normalizedPath);
    } else {
      setDeploymentPath('/');
    }

    // Set install packages for frontend - default to false (unchecked)
    if (containerType === 'frontend') {
      setInstallPackages(false);
    }

    // Mark manifest as loaded
    setHasManifestLoaded(true);

  }, [
    currentContainerConfig,
    containerType,
    isInitializing,
    selectedFramework,
    isCommandManuallyEdited,
    buildCommand,
    setCommand,
    setBuildCommand,
    setEnvVariables,
    setDeploymentPath,
    setInstallPackages
  ]);

  // CONSOLIDATED DIRECTORY FETCHING EFFECT
  useEffect(() => {
    if (!isOpen || !currentRepository || isInitializing || isLoadingDir || hasDirectoryFetched) {
      return;
    }

    // For backend, don't wait for manifest to load directory
    // For frontend, wait for manifest to be loaded
    if (isFrontend && !hasManifestLoaded) {
      return;
    }

    // Skip directory fetching if we already have a deployment path from manifest
    if (currentContainerConfig?.base_path && deploymentPath && deploymentPath !== '/') {
      setHasDirectoryFetched(true);
      return;
    }

    const baseDir = getBaseProjectDirectory();
    if (baseDir && (dirContents.length === 0 || currentPath !== baseDir)) {
      setHasDirectoryFetched(true);
      fetchDirectoryContents(baseDir);
    }
  }, [isOpen, currentRepository, hasManifestLoaded, isInitializing, isLoadingDir, hasDirectoryFetched, getBaseProjectDirectory, fetchDirectoryContents, dirContents.length, currentPath, isFrontend, currentContainerConfig?.base_path, deploymentPath]);

  // Fetch deployments for the current project
  const fetchDeploymentsList = useCallback(async () => {
    // Skip if already tried or if still loading
    if (isLoadingDeployments || hasTriedFetching) {
      return;
    }

    if (!currentRepository || !projectId) {
      return;
    }

    setIsLoadingDeployments(true);
    setHasTriedFetching(true);

    try {
      const response = await listDeployments(projectId);
      
      // Ensure response is an array
      if (!Array.isArray(response)) {
        console.warn('Deployments response is not an array:', response);
        setDeployments([]);
        return;
      }
      
      // Log all deployments before filtering for debugging
      console.log('All deployments before filtering:', response.map(d => ({
        deployment_id: d.deployment_id,
        app_id: d.app_id,
        status: d.status
      })));
      
      // Filter deployments by type (frontend/backend) based on deployment_id prefix
      const filteredDeployments = response.filter((deployment: Deployment) => {
        // Handle null or undefined deployment_id
        if (!deployment.deployment_id) {
          console.warn('Deployment missing deployment_id:', deployment);
          return false;
        }
        
        // Always exclude backend deployments from the existing deployments list
        if (deployment.deployment_id.startsWith('backend_')) {
          console.log('Excluding backend deployment:', deployment.deployment_id);
          return false;
        }
        
        // Additional checks for backend deployments (case-insensitive)
        const deploymentIdLower = deployment.deployment_id.toLowerCase();
        if (deploymentIdLower.includes('backend') || 
            deploymentIdLower.includes('api') || 
            deploymentIdLower.includes('server') ||
            deploymentIdLower.includes('service')) {
          console.log('Excluding potential backend deployment:', deployment.deployment_id);
          return false;
        }
        
        // Check if deployment has backend-specific properties
        if (deployment.app_id?.toLowerCase().includes('backend') ||
            deployment.app_id?.toLowerCase().includes('api')) {
          console.log('Excluding backend deployment by app_id:', deployment.deployment_id);
          return false;
        }
        
        // For frontend container type, include frontend deployments and legacy deployments
        if (containerType === 'frontend') {
          const isFrontend = deployment.deployment_id.startsWith('frontend_') || 
                            !deployment.deployment_id.includes('_') ||
                            deployment.deployment_id.toLowerCase().includes('frontend');
          return isFrontend;
        }
        
        // For backend container type, don't show any existing deployments (backend always creates new)
        if (containerType === 'backend') {
          return false;
        }
        
        // Default: only show frontend deployments
        return deployment.deployment_id.startsWith('frontend_') || 
               !deployment.deployment_id.includes('_') ||
               deployment.deployment_id.toLowerCase().includes('frontend');
      });
      
      const sortedDeployments = filteredDeployments.sort((a: Deployment, b: Deployment) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      
      // Final check to ensure no backend deployments are included
      const finalDeployments = sortedDeployments.filter(deployment => {
        const isBackend = deployment.deployment_id.toLowerCase().includes('backend') ||
                         deployment.app_id?.toLowerCase().includes('backend');
        if (isBackend) {
          console.warn('Backend deployment found in final list, removing:', deployment.deployment_id);
          return false;
        }
        return true;
      });
      
      console.log(`Found ${finalDeployments.length} frontend deployments (backend deployments excluded):`, finalDeployments);
      setDeployments(finalDeployments);

      // Auto-select the most recent deployment of the correct type if deployments exist and none is currently selected
      if (finalDeployments.length > 0 && !selectedDeployment) {
        setSelectedDeployment(finalDeployments[0]);
      }
    } catch (error) {
      console.error('Error fetching deployments:', error);
      setDeployments([]);
    } finally {
      setIsLoadingDeployments(false);
    }
  }, [currentRepository, isLoadingDeployments, selectedDeployment, projectId, hasTriedFetching, containerType]);

  // Fetch deployments when modal opens and repository is available (non-blocking)
  useEffect(() => {
    if (isOpen && currentRepository && !hasTriedFetching) {
      console.log('Modal opened, fetching deployments...', {
        isOpen,
        currentRepository: currentRepository?.name,
        hasTriedFetching,
        containerType
      });
      // Fetch deployments in background without blocking UI
      setTimeout(() => {
        fetchDeploymentsList();
      }, 100);
    }
  }, [isOpen, currentRepository, hasTriedFetching, fetchDeploymentsList]);

  // Add click outside handler for deployments dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showDeploymentsDropdown && !(event.target as Element).closest('.deployments-dropdown')) {
        setShowDeploymentsDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDeploymentsDropdown]);

  // Auto-select the most recent deployment when deployments are available 
  useEffect(() => {
    if (deployments.length > 0 && !selectedDeployment) {
      if (containerType === 'frontend' && !isCreateNewApp) {
        // For frontend, auto-select the most recent deployment
        setSelectedDeployment(deployments[0]);
      }
      // For backend, don't auto-select since backend always creates new deployments
    }
  }, [deployments, selectedDeployment, isCreateNewApp, containerType]);

  // Update selectedFolder when deploymentPath changes - DEBOUNCED
  useEffect(() => {
    const timer = setTimeout(() => {
      if (deploymentPath && deploymentPath !== '/') {
        const pathParts = deploymentPath.split('/').filter(Boolean);
        if (pathParts.length > 0) {
          setSelectedFolder(pathParts[pathParts.length - 1]);
        }
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [deploymentPath]);

  // Handle selection of first directory when directory contents are loaded
  useEffect(() => {
    if (taskId?.startsWith('cg') && dirContents.length > 0 && isOpen && !hasManifestLoaded) {
      // Skip auto-selection if we have a manifest with base_path
      if (currentContainerConfig?.base_path) {
        return;
      }
      
      // First try to find a folder that's not named "logs" and not hidden
      let selectedDir = dirContents.find((item: DirContent) =>
        item.type === 'folder' && !item.name.startsWith('.') && item.name.toLowerCase() !== 'logs'
      );

      // If no folder other than "logs" is found, fall back to the first folder
      if (!selectedDir) {
        selectedDir = dirContents.find((item: DirContent) =>
          item.type === 'folder' && !item.name.startsWith('.')
        );
      }

      if (selectedDir && (!deploymentPath || deploymentPath === '/')) {
        // Only set if no path is already set from manifest
        const newPath = `/${selectedDir.name}`;
        setDeploymentPath(newPath);
        setSelectedFolder(selectedDir.name);
      }
    }
  }, [dirContents, taskId, isOpen, hasManifestLoaded, deploymentPath, setDeploymentPath, currentContainerConfig]);

  // Apply settings from selected deployment
  const selectDeployment = (deployment: Deployment) => {
    // Only allow frontend deployments to be selected
    if (deployment.deployment_id?.startsWith('backend_')) {
      console.warn('Cannot select backend deployment for existing deployments list');
      return;
    }
    
    // Validate that the deployment is of the correct type
    const deploymentType = deployment.deployment_id?.startsWith('frontend_') ? 'frontend' : 
                          deployment.deployment_id?.startsWith('backend_') ? 'backend' : null;
    
    // For legacy deployments without prefix, assume they are frontend
    const isCorrectType = deploymentType === containerType || 
                         (containerType === 'frontend' && !deploymentType);
    
    if (!isCorrectType) {
      console.warn(`Attempted to select ${deploymentType || 'legacy'} deployment for ${containerType} container`);
      return;
    }
    
    setSelectedDeployment(deployment);

    // Set command from deployment
    if (deployment.command) {
      // Check if command includes 'npm install &&' to determine installPackages
      const hasInstallCommand = deployment.command.includes('npm install &&');
      setInstallPackages(hasInstallCommand);

      // Extract the actual build command
      let deploymentCommand = deployment.command;
      if (hasInstallCommand) {
        deploymentCommand = deployment.command.split('npm install &&').pop()?.trim() || '';
      }

      // Set the command without the install part
      setCommand(deploymentCommand);
    }

    // Only set deployment path if we don't have a manifest base_path
    // This prevents overriding the manifest-based path when selecting existing deployments
    if (deployment.root_path && !currentContainerConfig?.base_path) {
      // Extract relative path from the root_path
      let relativePath = deployment.root_path;

      if (currentRepository?.name && relativePath.includes(currentRepository.name)) {
        const repoIndex = relativePath.indexOf(currentRepository.name);
        const pathAfterRepo = relativePath.substring(repoIndex + currentRepository.name.length);
        relativePath = pathAfterRepo || '/';
      }

      setDeploymentPath(relativePath);
    }

    // Close the dropdown
    setShowDeploymentsDropdown(false);
  };

  // Add a helper function to ensure deployment history opens reliably
  const ensureDeploymentHistoryOpens = useCallback(() => {
    // First close the modal
    onClose();
    
    // Then open deployment history panel with a more reliable approach
    if (onOpenDeploymentHistory) {
      // Use a longer delay and dispatch a custom event for more reliability
      setTimeout(() => {
        console.log('Attempting to open deployment history panel...');
        // Dispatch a custom event that can be listened for in the parent component
        const deploymentCompleteEvent = new CustomEvent('deploymentComplete', {
          detail: { openHistory: true }
        });
        window.dispatchEvent(deploymentCompleteEvent);
        
        // Also call the callback for backward compatibility
        onOpenDeploymentHistory();
      }, 500);
    }
  }, [onClose, onOpenDeploymentHistory]);

  // Listen for deployment status messages from WebSocket - MEMOIZED MESSAGE HANDLER
  const handleWebSocketMessage = useCallback((event: MessageEvent) => {
    try {
      const message = JSON.parse(event.data);

      // Handle deployment status updates (original format)
      if (message.type === 'deployment_status') {
        // Extract status from the message based on the data structure
        const statusData = message.data || message;
        const status = statusData.status || message.status;

        // Handle different status updates
        if (status === 'success' || status === 'completed' || status === 'Custom_domain_ready') {
          // Handle successful deployment
          setIsDeploying(false);
          setShowLaunchAnimation(false);
          
          // Open deployment history panel on success
          ensureDeploymentHistoryOpens();
        } else if (status === 'failed' || status === 'error') {
          // Handle failed deployment
          setIsDeploying(false);
          setShowLaunchAnimation(false);
          setDeploymentError(`Deployment failed: ${statusData.message || 'Unknown error'}`);
          
          // Open deployment history panel even on failure
          ensureDeploymentHistoryOpens();
        } else if (status === 'processing' || status === 'in_progress' || status === 'deploying') {
          // Handle in-progress deployment - don't close modal yet, wait for animation
          setIsDeploying(true);
        }
      }

      // Handle deploy_status updates (from Python code example)
      else if (message.type === 'deploy_status') {
        const statusData = message.data || message;
        const status = statusData.status || message.status;

        if (status === 'success' || status === 'completed' || status === 'Custom_domain_ready') {
          // Handle successful deployment
          setIsDeploying(false);
          setShowLaunchAnimation(false);
          
          // Open deployment history panel on success
          ensureDeploymentHistoryOpens();
        } else if (status === 'failed' || status === 'error') {
          // Handle failed deployment
          setIsDeploying(false);
          setShowLaunchAnimation(false);
          setDeploymentError(`Deployment failed: ${statusData.message || 'Unknown error'}`);
          
          // Open deployment history panel even on failure
          ensureDeploymentHistoryOpens();
        } else if (status === 'processing' || status === 'in_progress' || status === 'building' || status === 'deploying') {
          // Handle in-progress deployment - don't close modal yet, wait for animation
          setIsDeploying(true);
        }
      }
      
      // Handle project configuration responses (main logic is in DeploymentContext)
      else if (message.type === 'manifest') {
        // Project configuration is handled in the DeploymentContext, 
        // but we can add any UI-specific handling here if needed
        console.log('Manifest received:', message.data);
        setHasManifestLoaded(true);
        setIsInitializing(false);
      }

      // Handle start_deploy_response
      else if (message.type === 'start_deploy_response') {
        if (message.status === 'success' || message.success === true) {
          // Deployment initiated successfully - don't close modal yet, wait for animation
          setIsDeploying(true);
        } else {
          // Failed to initiate deployment
          setIsDeploying(false);
          setShowLaunchAnimation(false);
          setDeploymentError(`Failed to start deployment: ${message.message || 'Unknown error'}`);
        }
      }

      // Removed deployment status tracking
    } catch (error) {
      // Silent error handling
    }
  }, [containerType, onClose, ensureDeploymentHistoryOpens]);

  useEffect(() => {
    if (!wsConnection) return;

    wsConnection.addEventListener('message', handleWebSocketMessage);

    return () => {
      wsConnection.removeEventListener('message', handleWebSocketMessage);
    };
  }, [wsConnection, handleWebSocketMessage]);

  // Load saved configurations
  useEffect(() => {
    const storedConfigs = localStorage.getItem('deploymentConfigs');
    if (storedConfigs) {
      try {
        setSavedConfigs(JSON.parse(storedConfigs));
      } catch (e) {
        // Silent error handling
      }
    }
  }, []);

  const addEnvVariable = () => {
    const currentEnvVars = envVariables || [];
    setEnvVariables([
      ...currentEnvVars,
      { key: '', value: '', id: Date.now().toString() }
    ]);
    // Reset saved state when environment variables change
    setIsConfigurationSaved(false);
  };

  const removeEnvVariable = (id: string) => {
    const currentEnvVars = envVariables || [];
    setEnvVariables(currentEnvVars.filter((v: EnvVariable) => v.id !== id));
    // Reset saved state when environment variables change
    setIsConfigurationSaved(false);
  };

  const updateEnvVariable = (id: string, field: 'key' | 'value', newValue: string) => {
    const currentEnvVars = envVariables || [];
    setEnvVariables(
      currentEnvVars.map((v: EnvVariable) =>
        v.id === id ? { ...v, [field]: newValue } : v
      )
    );
    // Reset saved state when environment variables change
    setIsConfigurationSaved(false);
  };

  // Create environment variables string for preview
  const getEnvString = () => {
    return envVariables
      .filter((v: EnvVariable) => v.key.trim() !== '')
      .map((v: EnvVariable) => `${v.key}=${v.value}`)
      .join(' ');
  };

  // Apply platform preset
  const applyPlatformPreset = (platformKey: string) => {
    const preset = PLATFORM_PRESETS[platformKey];
    if (!preset) return;

    setSelectedPlatform(platformKey);
    setCommand(preset.command);
    setInstallPackages(preset.installPackages);

    // Deep clone the env variables to avoid reference issues
    const newEnvVars = preset.envVariables.map((v: EnvVariable) => ({
      ...v,
      // Replace ${repository_name} with actual repository name if available
      value: v.value.replace('${repository_name}', currentRepository?.name || '')
    }));

    setEnvVariables(newEnvVars);
    setDeploymentPath(preset.deploymentPath);
    setShowPlatformDropdown(false);
  };

  // Save current configuration
  const saveCurrentConfig = () => {
    // Prepare environment variables data
    const envData = (envVariables || []).reduce((acc: Record<string, string>, v: EnvVariable) => {
      if (v.key && v.key.trim() !== '') {
        acc[v.key] = v.value || '';
      }
      return acc;
    }, {});

    // Send configuration via WebSocket
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      try {
        setIsSavingConfig(true);
        
        const message = {
          type: "save_configuration",
          task_id: taskId,
          input_data: {
            env_data: envData,
            container_name: currentContainerConfig?.container_name,
            command: command,
            install_packages: installPackages,
            deployment_path: deploymentPath,
            build_command: containerType === 'backend' ? buildCommand : undefined
          }
        };

        wsConnection.send(JSON.stringify(message));
        
        // Simulate successful save (since WebSocket response handling may vary)
        setTimeout(() => {
          setIsSavingConfig(false);
          setShowSaveSuccess(true);
          setIsConfigurationSaved(true);
          
          // Hide success animation after 2 seconds
          setTimeout(() => {
            setShowSaveSuccess(false);
          }, 2000);
        }, 1000);
        
      } catch (error) {
        console.error('Error sending configuration:', error);
        setIsSavingConfig(false);
        alert('Failed to save configuration. Please try again.');
      }
    } else {
      alert('WebSocket connection is not available. Please refresh the page and try again.');
    }
  };

  // Apply saved configuration
  const applySavedConfig = (config: PresetConfig) => {
    setCommand(config.command);
    setInstallPackages(config.installPackages);
    setEnvVariables(JSON.parse(JSON.stringify(config.envVariables)));
    setDeploymentPath(config.deploymentPath);
    setShowSavedConfigsDropdown(false);
  };

  const navigateToPath = (folderName: string) => {
    let newPath = currentPath;

    // Handle navigation logic
    if (folderName === '..') {
      // Go up one directory
      const pathParts = currentPath.split('/');
      pathParts.pop();
      newPath = pathParts.join('/');
      // Clear selection when going up
      setSelectedFolder('');
    } else {
      // Go into the folder
      newPath = `${currentPath}/${folderName}`.replace(/\/+/g, '/');
    }

    fetchDirectoryContents(newPath);
  };

  const handleFolderSelect = (folderName: string) => {
    // Toggle selection
    if (selectedFolder === folderName) {
      setSelectedFolder('');
    } else {
      setSelectedFolder(folderName);
    }
  };

  const selectPath = (path: string) => {
    let pathToUse = path;
    let folderPath = '';

    if (selectedFolder) {
      // If a specific folder is selected, append it to the path
      folderPath = `${path}/${selectedFolder}`.replace(/\/+/g, '/');
    } else {
      // Otherwise use the current path
      folderPath = path;
    }

    // Convert the repository path to relative path
    let finalPath = folderPath;
    if (currentRepository?.name && folderPath.includes(currentRepository.name)) {
      const repoIndex = folderPath.indexOf(currentRepository.name);
      finalPath = folderPath.substring(repoIndex);

      // If it starts with the repo name, make it relative
      if (finalPath.startsWith(currentRepository.name)) {
        finalPath = finalPath.replace(currentRepository.name, '');
      }
    }

    // Ensure the path starts with a slash
    if (!finalPath.startsWith('/')) {
      finalPath = '/' + finalPath;
    }

    // If no folder is explicitly selected and we're at root, use "/"
    if (!selectedFolder && (finalPath === '' || finalPath === '/' || finalPath === '/')) {
      finalPath = '/';
    }

    setDeploymentPath(finalPath || '/');
    setShowDirBrowser(false);
    setSelectedFolder('');
  };

  // Check if the current path is root
  const isRootPath = () => {
    // Check if we're at the repo root or absolute root
    if (!currentPath || currentPath === '/' || currentPath === currentRepository?.name || currentPath?.endsWith(`/${currentRepository?.name}`)) {
      return true;
    }
    
    // Also check if we're at the base project directory level
    const baseProjectDir = getBaseProjectDirectory();
    return currentPath === baseProjectDir || currentPath?.endsWith(`/${baseProjectDir}`);
  };

  // Format the display of deployment path to only show relative path
  const formatDisplayPath = (path: string): string => {
    if (!path) return '/';

    // For absolute paths that look like file system paths (e.g., /home/<USER>
    if (path.includes('/home/') || path.includes('/Users/')) {
      return '/';
    }

    // For manifest-provided base_paths, preserve the complete path structure
    // Don't strip repository names from manifest base_paths as they contain important project structure
    if (currentContainerConfig?.base_path && path.includes(currentContainerConfig.base_path)) {
      // If this is a manifest base_path, preserve it as-is
      const normalizedPath = path.startsWith('/') ? path : `/${path}`;
      return normalizedPath;
    }

    // For other paths that contain the repository name, strip it out
    if (currentRepository?.name && path.includes(currentRepository.name)) {
      // Find where the repo name occurs and take everything after it
      const pathParts = path.split(currentRepository.name);
      if (pathParts.length > 1) {
        const afterRepo = pathParts[pathParts.length - 1];
        return afterRepo || '/';
      }
    }

    // If it's already a relative path starting with slash, just return it
    if (path.startsWith('/')) return path;

    // Otherwise, add a leading slash
    return `/${path}`;
  };

  // Helper function to generate UUID with deployment type prefix
  const generateUUID = (deploymentType?: string) => {
    // Simple UUID generation that's compatible with all browsers
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    
    // Add deployment type prefix to ensure uniqueness between frontend and backend
    const prefix = deploymentType || containerType;
    return `${prefix}_${uuid}`;
  };

  // Handle launch deployment button
  const handleLaunch = () => {
    setDeploymentError(null);

    // Generate unique ID for the deployment based on selection
    // For backend: always create new with backend-specific ID
    // For frontend: create new only if isCreateNewApp is checked or no deployment is selected
    const shouldCreateNew = isBackend || isCreateNewApp || !selectedDeployment;
    
    // Generate deployment ID with type-specific prefix
    let deployment_id: string;
    if (shouldCreateNew) {
      // Create new deployment with type-specific ID
      deployment_id = generateUUID(containerType);
    } else {
      // Use existing deployment ID, but ensure it's for the correct type
      if (selectedDeployment?.deployment_id) {
        // Check if the selected deployment is for the same type
        const selectedDeploymentType = selectedDeployment.deployment_id.startsWith('frontend_') ? 'frontend' : 
                                     selectedDeployment.deployment_id.startsWith('backend_') ? 'backend' : null;
        
        if (selectedDeploymentType === containerType) {
          deployment_id = selectedDeployment.deployment_id;
        } else {
          // Wrong type, generate new ID
          deployment_id = generateUUID(containerType);
        }
      } else {
        deployment_id = generateUUID(containerType);
      }
    }

    

    // Send the deployment data via WebSocket
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      try {
        if (containerType === 'backend') {
          // Backend deployment - use start_container_deployment
          const backendPayload = {
            type: "start_container_deployment",
            task_id: taskId,
            input_data: {
              build_command: buildCommand,
              run_command: command, // Use the start command for run_command
              port: currentContainerConfig?.ports?.toString() || "3001",
              framework: currentContainerConfig?.framework || "fastapi",
              base_path: currentContainerConfig?.base_path || "",
            }
          };

          wsConnection.send(JSON.stringify(backendPayload));
        } else {
          // Frontend deployment - use existing start_deploy
          // Create environment variables string for command
          const envString = envVariables
            .filter((v: EnvVariable) => v.key.trim() !== '')
            .map((v: EnvVariable) => `${v.key}=${v.value}`)
            .join(' ');

          // Create full command with env variables
          let commandString = '';
          
          // Add clean command if enabled
          if (cleanBuild) {
            commandString = 'rm -rf build dist out .next .nuxt .cache .vite .angular .svelte-kit node_modules/.cache && ';
          }
          
          if (containerType === 'frontend' && installPackages) {
            const installCmd = getPackageInstallCommand();
            commandString += `${installCmd} && ${envString ? `${envString} ${command}` : command}`;
          } else {
            commandString += envString ? `${envString} ${command}` : command;
          }

          // Create the deployment payload with dynamic deployment type
          const deploymentPayload: {
            id: string;
            deployment_type: string;
            command: string;
            root_path: string;
            env_variables: Record<string, string>;
            app_id?: string;
          } = {
            id: deployment_id,
            deployment_type: getDeploymentType(),
            command: commandString,
            root_path: (() => {
              if (!currentRepository?.path) return deploymentPath;
              
              // Handle manifest-based deployments
              if (currentContainerConfig?.base_path) {
                // For manifest-based deployments, the deploymentPath contains the full project structure
                // e.g., "/webtictactoe-61599-8763188e/tic_tac_toe_frontend"
                // We need to construct the path relative to the workspace root, not the repository root
                
                // Get the workspace root (e.g., "/home/<USER>/workspace/code-generation")
                const workspaceRoot = currentRepository.path.includes('/code-generation/') 
                  ? currentRepository.path.split('/code-generation/')[0] + '/code-generation'
                  : currentRepository.path.replace(/\/[^\/]+$/, ''); // Fallback: remove last directory
                
                // Remove leading slash from deploymentPath if present
                const cleanDeploymentPath = deploymentPath.startsWith('/') 
                  ? deploymentPath.substring(1) 
                  : deploymentPath;
                
                const finalPath = `${workspaceRoot}/${cleanDeploymentPath}`.replace(/\/+/g, '/');
                
                
                
                return finalPath;
              } else {
                // For non-manifest deployments, check if manifest has root_path
                if (manifest?.root_path) {
                  // If manifest has root_path, ensure deploymentPayload.root_path starts with it
                  let finalPath = deploymentPath;
                  
                  // If deploymentPath doesn't start with manifest.root_path, prepend it
                  if (!deploymentPath.startsWith(manifest.root_path)) {
                    // Remove leading slash from deploymentPath if present
                    const cleanDeploymentPath = deploymentPath.startsWith('/') 
                      ? deploymentPath.substring(1) 
                      : deploymentPath;
                    
                    // Ensure manifest.root_path ends with slash
                    const manifestRootPath = manifest.root_path.endsWith('/') 
                      ? manifest.root_path 
                      : `${manifest.root_path}/`;
                    
                    finalPath = `${manifestRootPath}${cleanDeploymentPath}`.replace(/\/+/g, '/');
                  } else {
                    // If deploymentPath already starts with manifest.root_path, use it as-is
                    finalPath = deploymentPath;
                  }
                  
                  return finalPath;
                } else {
                  // For non-manifest deployments without root_path, use the original logic
                  const legacyPath = `${currentRepository.path}${deploymentPath.startsWith('/') ? deploymentPath : `/${deploymentPath}`}`.replace(/\/+/g, '/');
                  
                  return legacyPath;
                }
              }
            })(),
            env_variables: envVariables.reduce((acc: Record<string, string>, v: EnvVariable) => {
              if (v.key.trim()) {
                acc[v.key] = v.value;
              }
              return acc;
            }, {})
          };

          // If updating an existing deployment, include the app_id
          // Only include app_id when updating (not creating new)
          if (!shouldCreateNew && selectedDeployment && selectedDeployment.app_id) {
            deploymentPayload.app_id = selectedDeployment.app_id;
          }

          const message = {
            type: "start_deploy",
            task_id: taskId,
            input_data: deploymentPayload
          };

          wsConnection.send(JSON.stringify(message));
        }

        // Set the deploying state
        setIsDeploying(true);
        setShowLaunchAnimation(true);
        
        // For both frontend and backend, close modal and open history panel after animation
        setTimeout(() => {
          setShowLaunchAnimation(false);
          setIsDeploying(false);
          ensureDeploymentHistoryOpens();
        }, 3000);

      } catch (error) {
        console.error('Deployment error:', error);
        setDeploymentError('Failed to send deployment data. Please try again.');
        setShowLaunchAnimation(false);
        
        // Even on error, we should open the deployment history panel
        // to show previous deployments
        ensureDeploymentHistoryOpens();
      }
    } else {
      setDeploymentError('WebSocket connection is not available. Please refresh the page and try again.');
      setShowLaunchAnimation(false);
      
      // Even with no WebSocket, we should open the deployment history panel
      // to show previous deployments
      ensureDeploymentHistoryOpens();
    }
  };

  // Rocket Launch Animation Component
  const RocketLaunchAnimation = () => {
    return (
      <div className="absolute inset-0 flex items-center justify-center z-20 backdrop-blur-md bg-white bg-opacity-75">
        <div className="relative flex flex-col items-center">
          {/* Enhanced rocket and flame */}
          <div className="relative mb-10 h-48">
            {/* Multiple flame layers for better effect */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-28 rounded-b-full bg-gradient-to-t from-primary-600 via-primary-500 to-transparent opacity-60 animate-pulse"></div>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-20 rounded-b-full bg-gradient-to-t from-yellow-500 via-yellow-400 to-transparent opacity-80 animate-flame"></div>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-5 h-16 rounded-b-full bg-gradient-to-t from-red-500 via-primary-300 to-transparent opacity-70 animate-flame-delay"></div>

            {/* Simple, clear rocket design */}
            <div className="relative z-10 animate-rocket">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="60" height="60" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                {/* Simple rocket based on Lucide Rocket icon */}
                <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z" stroke="hsl(var(--primary))" fill="hsl(var(--primary))" />
                <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z" stroke="hsl(var(--primary))" fill="hsl(var(--primary))" />
                <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0" stroke="hsl(var(--primary))" fill="hsl(var(--primary))" />
                <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5" stroke="hsl(var(--primary))" fill="hsl(var(--primary))" />
              </svg>
            </div>

            {/* Small particles/sparks for enhanced effect */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-20 h-8 flex justify-center">
              <div className="w-1 h-1 bg-yellow-300 rounded-full animate-spark-1 opacity-80"></div>
              <div className="w-1 h-1 bg-primary-400 rounded-full animate-spark-2 opacity-80"></div>
              <div className="w-1 h-1 bg-red-400 rounded-full animate-spark-3 opacity-80"></div>
              <div className="w-1 h-1 bg-yellow-300 rounded-full animate-spark-4 opacity-80"></div>
            </div>
          </div>

          <div className="text-primary font-weight-medium typography-body-lg">
            Launching Deployment
          </div>
          <div className="text-gray-600 mt-2 typography-body-sm">
            Preparing to blast off!
          </div>
        </div>
      </div>
    );
  };



  return (
    <>
      <style jsx global>{`
        @keyframes rocket {
          0% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-15px);
          }
          100% {
            transform: translateY(0px);
          }
        }

        .animate-rocket {
          animation: rocket 2s infinite ease-in-out;
        }

        @keyframes flame {
          0% {
            height: 16px;
            opacity: 0.7;
          }
          50% {
            height: 22px;
            opacity: 0.9;
          }
          100% {
            height: 16px;
            opacity: 0.7;
          }
        }

        .animate-flame {
          animation: flame 0.5s infinite ease-in-out;
        }

        @keyframes flame-delay {
          0% {
            height: 12px;
            opacity: 0.6;
          }
          50% {
            height: 18px;
            opacity: 0.8;
          }
          100% {
            height: 12px;
            opacity: 0.6;
          }
        }

        .animate-flame-delay {
          animation: flame-delay 0.5s infinite ease-in-out 0.15s;
        }

        @keyframes spark-1 {
          0% { transform: translate(-5px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(-10px, 10px); opacity: 0; }
        }

        @keyframes spark-2 {
          0% { transform: translate(5px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(12px, 12px); opacity: 0; }
        }

        @keyframes spark-3 {
          0% { transform: translate(-2px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(-7px, 15px); opacity: 0; }
        }

        @keyframes spark-4 {
          0% { transform: translate(8px, 0px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translate(15px, 10px); opacity: 0; }
        }

        .animate-spark-1 {
          animation: spark-1 1s infinite ease-out;
        }

        .animate-spark-2 {
          animation: spark-2 1.3s infinite ease-out 0.2s;
        }

        .animate-spark-3 {
          animation: spark-3 0.9s infinite ease-out 0.5s;
        }

        .animate-spark-4 {
          animation: spark-4 1.1s infinite ease-out 0.1s;
        }

        @keyframes success-pop {
          0% {
            transform: scale(0.8);
            opacity: 0;
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        .animate-success-pop {
          animation: success-pop 0.5s ease-out;
        }
      `}</style>

      <Drawer
        bodyClass='scrollbar-hide overflow-y-auto'
        isOpen={isOpen}
        onClose={() => {
          onClose();
        }}
        placement="right"
        overlayClassName="z-[1001] left-0"
        width={450}
        showBackdrop={false}
        theme="light"
        title={
          <div className="flex items-center gap-2">
            <span className="font-weight-medium">
              {containerType === 'backend' ? 'Backend' : 'Frontend'} Deployment Configuration
            </span>
          </div>
        }
      >
        <div className="flex flex-col h-full relative">


          {/* Rocket Launch Animation Overlay */}
          {showLaunchAnimation && <RocketLaunchAnimation />}

          <div 
            ref={contentRef}
            className={`overflow-y-auto px-[20px] py-[16px] transition-all duration-300 relative ${
              deploymentError ? 'pb-40' : 'pb-32'
            }`}
          >
            {/* Gradient fade indicator when deployment error is shown */}
            {deploymentError && (
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>
            )}
            
            {(
              <>
                {/* Header Info */}
                <div className="bg-primary-50 p-4 rounded-md mb-6">
              <div className="flex items-center gap-2 mb-2">
                <Rocket className="h-5 w-5 text-primary" />
                <h3 className="font-weight-semibold text-[14px] text-gray-800">
                  Configure {containerType === 'backend' ? 'Backend' : 'Frontend'} Deployment
                </h3>
              </div>
              <p className="typography-body-sm text-gray-600">
                Configure your {containerType} deployment settings for {currentRepository?.name || 'your project'}
              </p>
              {currentContainerConfig && (
                <div className="mt-2 flex items-center gap-2">
                  <span className="typography-caption text-gray-500">Framework:</span>
                  <div className="flex items-center gap-1">
                    {FRAMEWORK_LOGOS[currentContainerConfig.framework]}
                    <span className="typography-caption font-weight-medium text-gray-700 capitalize">
                      {currentContainerConfig.framework}
                    </span>
                  </div>
                </div>
              )}
            </div>



            {/* Create New App Section - Only show for backend */}
            {isBackend && (
              <div className="mb-6">
                <div className="border-b pb-2 mb-3">
                  <h2 className="font-weight-semibold text-[14px] text-gray-800">Backend Deployment</h2>
                </div>
                <div className="bg-primary-50 p-4 rounded-md">
                  <div className="flex items-center gap-2 mb-2">
                    <input
                      type="checkbox"
                      id="createNewBackendApp"
                      checked={true}
                      disabled={true}
                      className="rounded border-gray-300 text-primary focus:ring-primary-500"
                    />
                    <label htmlFor="createNewBackendApp" className="typography-body-sm text-gray-700 cursor-pointer font-weight-medium">
                      Create New Backend App
                    </label>
                  </div>
                  <p className="typography-caption text-gray-600 ml-6">
                    Backend deployments will create a new application instance
                  </p>
                </div>
              </div>
            )}



            {/* Existing Deployments Section - Only show for frontend */}
            {(deployments.length > 0 && containerType === 'frontend') && (
              <div className="mb-6">
                <div className="border-b pb-2 mb-3">
                  <h2 className="font-weight-semibold text-[14px] text-gray-800">Existing Deployments</h2>
                </div>
                {/* Removed Create New App option for frontend - only show existing deployments */}
                {/* 
                <div className="flex items-center justify-between mb-3">
                  <div className="flex flex-col">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="createNewApp"
                        checked={isCreateNewApp}
                        disabled={isDeploying || showLaunchAnimation}
                        onChange={(e) => {
                          const checked = e.target.checked;
                          setIsCreateNewApp(checked);

                          if (checked) {
                            // Create new app - clear selected deployment
                            setSelectedDeployment(null);
                          } else {
                            // Update existing app - select the most recent deployment
                            if (deployments.length > 0) {
                              setSelectedDeployment(deployments[0]);
                            }
                          }
                        }}
                        className="rounded border-gray-300 text-primary focus:ring-primary-500"
                      />
                      <label htmlFor="createNewApp" className="ml-2 typography-body-sm text-gray-700 cursor-pointer">
                        Create New App
                      </label>
                    </div>
                    <div className="ml-6 typography-caption text-gray-500 mt-1">
                      {isCreateNewApp
                        ? 'Creating a new app deployment'
                        : 'Updating the most recent deployment. Check above to create new instead.'}
                    </div>
                  </div>
                </div>
                */}
                <div className="relative deployments-dropdown">
                  <div className="flex gap-2">
                    <button
                      onClick={() => {
                        console.log('Deployment dropdown clicked', {
                          currentState: showDeploymentsDropdown,
                          deploymentsCount: deployments.length,
                          selectedDeployment: selectedDeployment?.deployment_id
                        });
                        setShowDeploymentsDropdown(!showDeploymentsDropdown);
                      }}
                      className="flex-1 p-3 rounded-md flex items-center justify-between typography-body-sm text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 bg-gray-50 border border-gray-200 hover:bg-gray-100"
                    >
                      <div className="flex items-center gap-2">
                        {isLoadingDeployments ? (
                          <>
                            <div className="h-4 w-4 rounded-full border-2 border-primary border-t-transparent animate-spin"></div>
                            <span>Loading deployments...</span>
                          </>
                        ) : (
                          <>
                            <History size={16} className={`${isCreateNewApp ? 'text-gray-400' : 'text-primary'}`} />
                            <span className="max-w-[250px]">
                              {selectedDeployment ? (
                                <div className="flex items-center gap-1">
                                  <div className="font-weight-medium max-w-[200px] truncate">
                                    <span className="font-weight-semibold truncate">{extractProjectNameFromPath(selectedDeployment.root_path)}</span>
                                    {selectedDeployment.app_id && (
                                      <span className="font-weight-normal text-gray-600 ml-1 truncate">- {selectedDeployment.app_id}</span>
                                    )}
                                  </div>
                                  <span className="typography-caption text-gray-500 ml-1 whitespace-nowrap">
                                    ({new Date(selectedDeployment.created_at).toLocaleDateString()})
                                  </span>
                                </div>
                              ) : (
                                'Select existing deployment'
                              )}
                            </span>
                          </>
                        )}
                      </div>
                      <ChevronDown size={16} className={`${(isBackend && isCreateNewApp) ? 'text-gray-400' : 'text-primary'} transition-transform ${showDeploymentsDropdown ? 'rotate-180' : ''}`} />
                    </button>
                    <button 
                      onClick={() => {
                        setHasTriedFetching(false);
                        fetchDeploymentsList();
                        if (selectedDeployment) {
                          setShowDeploymentsDropdown(true);
                        }
                      }}
                      title="Refresh deployments"
                      className="p-3 rounded-md text-gray-600 transition-colors bg-gray-50 hover:bg-gray-100"
                    >
                      <RefreshCw size={16} className={isLoadingDeployments ? "animate-spin" : ""} />
                    </button>
                  </div>

                  {showDeploymentsDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 max-h-60 overflow-y-auto">
                      {deployments.length > 0 ? (
                        <ul className="py-1">
                          {deployments.map((deployment: Deployment) => (
                            <li key={deployment.deployment_id} className="border-b border-gray-100 last:border-b-0">
                              <button
                                onClick={() => selectDeployment(deployment)}
                                className={`w-full text-left px-4 py-3 typography-body-sm hover:bg-gray-50 flex items-center gap-3 ${
                                  selectedDeployment?.deployment_id === deployment.deployment_id ? 'bg-primary-50 text-primary-600' : ''
                                }`}
                              >
                                <div className="flex-shrink-0">
                                  {selectedDeployment?.deployment_id === deployment.deployment_id ? (
                                    <Check size={18} className="text-primary" />
                                  ) : (
                                    <Rocket size={18} className={
                                      deployment.status === 'success' ? 'text-green-500' :
                                      deployment.status === 'failed' ? 'text-red-500' :
                                      deployment.status === 'processing' ? 'text-primary' : 'text-gray-500'
                                    } />
                                  )}
                                </div>
                                <div className="flex flex-col flex-1 min-w-0">
                                  <div className="font-weight-medium typography-body-sm truncate">
                                    {/* Display project name and app_id clearly */}
                                    <span className="font-weight-semibold truncate">{extractProjectNameFromPath(deployment.root_path)}</span>
                                    {deployment.app_id && (
                                      <span className="font-weight-normal text-gray-600 ml-1 truncate">- {deployment.app_id}</span>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2 typography-caption text-gray-500 mt-1">
                                    <span className="whitespace-nowrap">{new Date(deployment.created_at).toLocaleDateString()}</span>
                                    <span className="w-1 h-1 rounded-full bg-gray-300 flex-shrink-0"></span>
                                    <span className={`font-weight-medium whitespace-nowrap ${
                                      deployment.status === 'success' ? 'text-green-500' :
                                      deployment.status === 'failed' ? 'text-red-500' :
                                      deployment.status === 'processing' ? 'text-primary' : 'text-gray-500'
                                    }`}>
                                      {deployment.status}
                                    </span>
                                  </div>
                                </div>
                              </button>
                            </li>
                          ))}
                        </ul>
                      ) : isLoadingDeployments ? (
                        <div className="p-4 text-center text-gray-500">
                          <div className="inline-block h-4 w-4 rounded-full border-2 border-primary border-t-transparent animate-spin mr-2"></div>
                          Loading deployments...
                        </div>
                      ) : (
                        <div className="p-4 text-center">
                          <p className="text-gray-500 mb-2">No deployments available</p>
                          <button
                            onClick={() => {
                              setHasTriedFetching(false);
                              fetchDeploymentsList();
                            }}
                            className="px-3 py-1 typography-caption text-primary-600 border border-primary-300 rounded-md hover:bg-primary-50"
                          >
                            <RefreshCw size={12} className="inline mr-1" />
                            Refresh
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                <p className="typography-caption text-gray-500 mt-2">
                  Select an existing frontend deployment to update its configuration
                </p>
              </div>
            )}



            {/* Deployment Path */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3 flex items-center justify-between">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">
                  Deployment Path ({containerType === 'backend' ? 'Backend' : 'Frontend'})
                </h2>
                <div className="flex items-center gap-2">
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={formatDisplayPath(deploymentPath)}
                    onChange={(e) => {
                      // When user edits, treat it as a relative path
                      const newPath = e.target.value;
                      setDeploymentPath(newPath);
                    }}
                    placeholder="/"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary typography-body-sm"
                  />
                  <button
                    onClick={() => {
                      setShowDirBrowser(true);
                      setTimeout(() => {
                        const baseDir = getBaseProjectDirectory();
                        
                        fetchDirectoryContents(baseDir);
                      }, 100);
                    }}
                    className="px-3 py-2 typography-body-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-primary-500 relative"
                    title="Browse folders"
                    disabled={isLoadingDir}
                  >
                    {isLoadingDir ? (
                      <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full mx-auto"></div>
                    ) : (
                      <Folder size={16} className="text-gray-600" />
                    )}
                  </button>
                  {/* Manual retry button when loading is stuck */}
                  {isLoadingDir && (
                    <button
                      onClick={() => {
                        const baseDir = getBaseProjectDirectory();
                        fetchDirectoryContents(baseDir);
                      }}
                      className="ml-2 px-2 py-2 typography-caption text-gray-500 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-primary-500"
                      title="Retry directory loading"
                    >
                      <RefreshCw size={14} />
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Directory Browser Modal */}
            {showDirBrowser && (
              <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-4 m-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="font-weight-medium text-gray-900">Select Deployment Folder</h3>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 w-2 rounded-full ${
                        wsConnection?.readyState === WebSocket.OPEN
                          ? 'bg-green-500'
                          : 'bg-red-500'
                      }`}></div>
                      <button
                        onClick={() => {
                          // Use current path or fall back to base project directory
                          const refreshDir = currentPath || getBaseProjectDirectory();
                          fetchDirectoryContents(refreshDir);
                        }}
                        className="text-gray-500 hover:text-gray-700 p-1 relative"
                        title="Refresh"
                        disabled={isLoadingDir}
                      >
                        {isLoadingDir ? (
                          <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                        ) : (
                          <RefreshCw size={14} />
                        )}
                      </button>
                      <button
                        onClick={() => setShowDirBrowser(false)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  </div>

                  <div className="typography-caption text-gray-500 mb-2">
                    <div className="flex justify-between items-center">
                      <span>Select a folder or double-click to navigate</span>
                      {selectedFolder && <span className="text-primary-600">Selected: {selectedFolder}</span>}
                    </div>
                    <div className="mt-1 px-2 py-1 bg-gray-100 rounded text-xs font-mono">
                      Current path: {currentPath || getBaseProjectDirectory()}
                    </div>
                    {wsConnection?.readyState !== WebSocket.OPEN && (
                      <div className="mt-1 px-2 py-1 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                        ⚠️ WebSocket connection is not active. Directory browsing may not work.
                      </div>
                    )}
                  </div>

                  <div className="border border-gray-200 rounded-md max-h-60 overflow-y-auto mb-4">
                    {isLoadingDir ? (
                      <div className="p-4 text-center text-gray-500">
                        <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                        Loading...
                      </div>
                    ) : dirContents.length > 0 ? (
                      <ul className="divide-y divide-gray-200">
                        {/* Parent directory option - only show if not at root */}
                        {currentPath && !isRootPath() && (
                          <li
                            className="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center gap-2"
                            onClick={() => navigateToPath('..')}
                          >
                            <Folder size={16} className="text-gray-500" />
                            <span className="text-gray-500">..</span>
                          </li>
                        )}

                        {/* Filter to only show folders and exclude hidden folders (starting with .) */}
                        {dirContents
                          .filter((item: DirContent) => item.type === 'folder' && !item.name.startsWith('.'))
                          .map((item: DirContent, index: number) => (
                            <li
                              key={index}
                              className={`px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center gap-2 ${
                                selectedFolder === item.name ? 'bg-primary-50' : ''
                              }`}
                              onClick={() => handleFolderSelect(item.name)}
                              onDoubleClick={() => navigateToPath(item.name)}
                            >
                              <div className="flex items-center justify-center w-5 h-5">
                                <input
                                  type="checkbox"
                                  checked={selectedFolder === item.name}
                                  onChange={() => handleFolderSelect(item.name)}
                                  className="rounded border-gray-300 text-primary focus:ring-primary-500"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              </div>
                              <Folder size={16} className={`${selectedFolder === item.name ? 'text-primary' : 'text-gray-500'}`} />
                              <span className={selectedFolder === item.name ? 'font-weight-medium text-primary-700' : ''}>{item.name}</span>
                            </li>
                          ))
                        }
                      </ul>
                    ) : (
                      <div className="p-4 text-center">
                        <p className="text-gray-500 mb-2">No folders available</p>
                        <button
                          onClick={() => {
                            // Use base project directory for retry
                            const retryDir = getBaseProjectDirectory();
                            fetchDirectoryContents(retryDir);
                          }}
                          className="px-3 py-1 typography-caption text-primary-600 border border-primary-300 rounded-md hover:bg-primary-50 flex items-center justify-center gap-1"
                          disabled={isLoadingDir}
                        >
                          {isLoadingDir ? (
                            <div className="animate-spin h-3 w-3 border-2 border-primary border-t-transparent rounded-full mr-1"></div>
                          ) : (
                            <RefreshCw size={12} />
                          )}
                          Retry
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end gap-3">
                    <button
                      onClick={() => setShowDirBrowser(false)}
                      className="px-3 py-1.5 typography-body-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => selectPath(currentPath)}
                      className="px-3 py-1.5 typography-body-sm text-white bg-primary hover:bg-primary-600 rounded-md"
                    >
                      {selectedFolder
                        ? `Select "${selectedFolder}"`
                        : isRootPath()
                          ? 'Select Root (/)'
                          : 'Select Current Path'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Environment Variables */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3 flex justify-between items-center">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">Environment Variables</h2>
                <button
                  onClick={addEnvVariable}
                  className="text-primary hover:text-primary-600 transition-colors focus:outline-none"
                >
                  <Plus size={16} />
                </button>
              </div>
              <div className="space-y-3">
                {envVariables && envVariables.length > 0 ? (
                  envVariables.map((variable: EnvVariable) => (
                    <div key={variable.id} className="flex items-center gap-3">
                      <input
                        type="text"
                        value={variable.key || ''}
                        onChange={(e) => updateEnvVariable(variable.id, 'key', e.target.value)}
                        placeholder="KEY"
                        className="w-44 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500 typography-body-sm uppercase"
                      />
                      <span className="text-gray-400 text-lg font-weight-medium">=</span>
                      <input
                        type="text"
                        value={variable.value || ''}
                        onChange={(e) => updateEnvVariable(variable.id, 'value', e.target.value)}
                        placeholder="value"
                        className="w-44 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500 typography-body-sm"
                      />
                      <button
                        onClick={() => removeEnvVariable(variable.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors focus:outline-none p-1"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  ))
                ) : (
                  <p className="typography-body-sm text-gray-500 italic">No environment variables defined</p>
                )}
                <p className="typography-caption text-gray-500">
                  Define environment variables for your deployment
                </p>
              </div>
            </div>

            {/* Command */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3 flex items-center justify-between">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">
                  Start Command
                </h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFrameworkDropdown(!showFrameworkDropdown)}
                    className="typography-caption bg-gray-50 border border-gray-200 rounded-md px-2 py-1 text-gray-600 hover:text-primary hover:border-primary-300 flex items-center gap-1"
                  >
                    {selectedFramework
                      ? <><span className="mr-1">{FRAMEWORK_LOGOS[selectedFramework]}</span><span className="font-weight-medium capitalize">{selectedFramework}</span></>
                      : currentContainerConfig?.framework
                        ? <><span className="mr-1">{FRAMEWORK_LOGOS[currentContainerConfig.framework]}</span><span className="font-weight-medium capitalize">{currentContainerConfig.framework}</span></>
                        : `${containerType === 'backend' ? 'Backend' : 'Frontend'} Framework Presets`}
                    <ChevronDown size={12} className={`transition-transform ${showFrameworkDropdown ? 'rotate-180' : ''}`} />
                  </button>
                  {showFrameworkDropdown && (
                    <div className="absolute right-0 mt-1 w-56 bg-white shadow-md rounded-md border border-gray-200 z-10">
                      <div className="py-1 px-2 border-b border-gray-100 typography-caption text-gray-500">
                        Select {containerType} framework command
                      </div>
                      <ul className="py-1 max-h-60 overflow-y-auto">
                        {getFrameworksForType().map(([key, {command: frameworkCommand, description}]) => (
                          <li key={key}>
                            <button
                              onClick={() => {
                                setCommand(frameworkCommand);
                                setSelectedFramework(key);
                                setShowFrameworkDropdown(false);
                                setIsCommandManuallyEdited(false); // Reset manual edit flag when selecting preset
                              }}
                              className={`w-full text-left px-3 py-2 typography-body-sm hover:bg-gray-50 flex items-center gap-2 ${selectedFramework === key ? 'bg-primary-50 text-primary-600' : ''}`}
                            >
                              <span className="flex-shrink-0">{FRAMEWORK_LOGOS[key]}</span>
                              <span className="font-weight-medium capitalize">{key}</span>
                              <span className="typography-caption text-gray-500 ml-auto">{description}</span>
                              {selectedFramework === key && (
                                <Check size={14} className="text-primary ml-1 flex-shrink-0" />
                              )}
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                {/* Start Command */}
                <div className="flex items-center gap-2 mb-2">
                  {selectedFramework ? (
                    <span className="flex-shrink-0">{FRAMEWORK_LOGOS[selectedFramework]}</span>
                  ) : currentContainerConfig?.framework ? (
                    <span className="flex-shrink-0">{FRAMEWORK_LOGOS[currentContainerConfig.framework] || <Terminal className="h-4 w-4 text-gray-500" />}</span>
                  ) : (
                    <Terminal className="h-4 w-4 text-gray-500" />
                  )}
                  <input
                    type="text"
                    value={command}
                    onChange={(e) => {
                      setCommand(e.target.value);
                      setIsCommandManuallyEdited(true);
                    }}
                    placeholder={isBackend ? 'uvicorn main:app --host 0.0.0.0 --port 8000' : 'npm run build'}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary typography-body-sm"
                  />
                </div>

                {/* Build Command - Only for Backend */}
                {containerType === 'backend' && (
                  <div className="space-y-2">
                    <h3 className="font-weight-medium text-[13px] text-gray-700">Build Command</h3>
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-gray-500" />
                      <input
                        type="text"
                        value={buildCommand}
                        onChange={(e) => setBuildCommand(e.target.value)}
                        placeholder="pip install -r requirements.txt"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary typography-body-sm"
                      />
                    </div>
                  </div>
                )}

                {/* Package Installation - Only for Frontend */}
                {containerType === 'frontend' && (
                  <div className="p-3 bg-gray-50 rounded-md">
                    <label className="flex items-start gap-2 cursor-pointer">
                      <div className="pt-0.5">
                        <input
                          type="checkbox"
                          checked={installPackages}
                          onChange={(e) => setInstallPackages(e.target.checked)}
                          className="rounded border-gray-300 text-primary focus:ring-primary-500"
                        />
                      </div>
                      <div>
                        <div className="flex items-center gap-1.5 typography-body-sm font-weight-medium text-gray-700">
                          <Package className="h-4 w-4 text-gray-500" />
                          Install packages first
                        </div>
                        <p className="typography-caption text-gray-500 mt-1">
                          Run {getPackageInstallCommand()} before building your application
                        </p>
                      </div>
                    </label>
                  </div>
                )}
              </div>
            </div>

            {/* Preview Command */}
            <div className="mb-6">
              <div className="border-b pb-2 mb-3">
                <h2 className="font-weight-semibold text-[14px] text-gray-800">Command Preview</h2>
              </div>
              <div className="bg-gray-800 text-green-400 typography-body-sm p-3 rounded-md overflow-x-auto whitespace-pre-wrap">
                {containerType === 'backend' ? (
                  <>
                    {cleanBuild && (
                      <>
                        <span className="text-primary-300"># Clean Command:</span><br/>
                        <span className="text-yellow-300">$ rm -rf build dist out .next .nuxt .cache .vite .angular .svelte-kit node_modules/.cache</span><br/><br/>
                      </>
                    )}
                    <span className="text-primary-300"># Build Command:</span><br/>
                    <span className="text-yellow-300">$ {buildCommand}</span><br/><br/>
                    <span className="text-primary-300"># Start Command:</span><br/>
                    <span className="text-yellow-300">$ {command}</span>
                  </>
                ) : (
                  <>
                    <span className="text-yellow-300">$ {fullCommand}</span>
                  </>
                )}
              </div>
            </div>

            {/* Save Configuration - Only show when environment variables exist */}
            {envVariables && envVariables.length > 0 && envVariables.some((v: EnvVariable) => v.key && v.key.trim() !== '' && v.value && v.value.trim() !== '') && (
              <div className="mb-6">
                <button
                  onClick={saveCurrentConfig}
                  disabled={isSavingConfig || isConfigurationSaved}
                  className={`w-full flex items-center justify-center gap-2 px-4 py-2 typography-body-sm font-weight-medium rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-300 disabled:cursor-not-allowed ${
                    showSaveSuccess || isConfigurationSaved
                      ? 'text-white bg-green-500 border border-green-500' 
                      : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50'
                  }`}
                >
                  {isSavingConfig ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                      Saving...
                    </>
                  ) : showSaveSuccess || isConfigurationSaved ? (
                    <>
                      <div className="animate-success-pop">
                        <Check size={14} className="text-white" />
                      </div>
                      Configuration Saved!
                    </>
                  ) : (
                    <>
                      <Save size={14} className="text-gray-700" />
                      Save Configuration
                    </>
                  )}
                </button>
                <p className="typography-caption text-gray-500 mt-1 text-center">
                  {showSaveSuccess || isConfigurationSaved
                    ? 'Configuration saved successfully for future deployments'
                    : 'Save this configuration for future deployments'
                  }
                </p>
              </div>
            )}
              </>
            )}
          </div>

          {/* Launch Button - Update text based on container type */}
          <div className="absolute bottom-0 left-0 right-0 pt-4 pb-5 px-[20px] border-t bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)]">
            {/* Scroll indicator when deployment error is shown */}
            {deploymentError && (
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gray-300 rounded-full opacity-50"></div>
            )}
            {deploymentError && (
              <div className="mb-3 p-2 bg-red-50 border border-red-300 rounded-md">
                <p className="typography-body-sm text-red-600">{deploymentError}</p>
              </div>
            )}
            <button
              onClick={handleLaunch}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 typography-body-sm font-weight-medium text-white bg-primary hover:bg-primary-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
            >
              <Rocket className="h-4 w-4" />
              {(() => {
                if (containerType === 'backend') {
                  return 'Create Backend Deployment';
                } else {
                  // Frontend logic based on whether we're creating new or updating
                  return (isCreateNewApp || !selectedDeployment) ? 'Create Frontend Deployment' : 'Update Frontend Deployment';
                }
              })()}
            </button>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default DeploymentInterface;