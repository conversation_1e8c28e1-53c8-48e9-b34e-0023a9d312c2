import React, { useState, useRef, useEffect, useContext, useMemo, useCallback, useReducer } from "react";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import {
  Loader2,
  Maximize,
  RotateCw,
  Link2,
  X,
  ExternalLink,
  Minimize,
  Plus,
  BookOpen,
  Download,
  Rocket,
  RefreshCw,
  MoreVertical
} from "lucide-react";
import CodeDiscussionPanel from "@/components/CodeDiscussionPanel";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useDeployment } from "@/components/Context/DeploymentContext";
import CodeViewPanel from "@/components/CodeGenrationPanel/CodeViewPanel/CodeViewPanel";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";


import { updateTask } from "@/utils/batchAPI";
import { usePlanRestriction } from "@/components/Context/PlanRestrictionContext";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import GitSidebar from "./GitInterface";
import TaskProgress from "./TaskProgress";
import useFullscreenHandler from "./useFullscreenHandler";
import DeploymentInterface from "./DeploymentInterface";
import { listDeployments, deleteDeployment } from "@/utils/deploymentApi";
import DocumentsRenderPanel from "@/components/CodeGenrationPanel/DocumentsPanel/DocumentsRenderPanel";

import Cookies from "js-cookie";
import ModelSelector from "@/components/ModelSelector/ModelSelector";
import { getPreviewUrl as getPreviewUrlFromBackend } from "@/utils/url_helpers";
import PreviewPanel from "@/components/CodeGenrationPanel/PreviewPanel/PreviewPanel";
import FigmaPreviewPanel from "@/components/CodeGenrationPanel/PreviewPanel/FigmaPreviewPanel";
import PreviewContainers from "@/components/CodeGenrationPanel/PreviewPanel/PreviewContainers";
import InactivityTimerModal from "./InactivityModal";
import { getLatestSubscription } from "@/utils/paymentAPI";
import { decryptToken } from "@/utils/auth";
import CreditBadge from "@/components/ui/CreditBadge"
import { mergeToKaviaMain } from "@/utils/batchAPI";
import SessionLimitModal from "@/components/CodeGenrationPanel/SessionLimitModal";
import { discardAndExit } from "@/utils/gitAPI";
import { getFigmaJsonFiles } from "@/utils/FigmaAPI";
import { getFigmaDesignList ,getFigmaFilesScreens} from "@/api/figma";

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url) => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

// Custom debounce utility to prevent cascading re-renders

const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Extract tab button rendering into memoized component
const TabButtons = React.memo(({
  currentTaskId,
  activeTab,
  toggleTab,
  documentContent,
  newDocAlert,
  projectId,
  setFigmaData,
  figmaData,
  setFigmaLoader
}) => {
  const isDeepQuery = currentTaskId?.startsWith("deep-query-job");
  const handleFigmaTabClick = async () => {
    setFigmaLoader(true)
    toggleTab("Figma");

    try {
      // Step 1: Get Figma design list to extract full figma_id
      const initialResult = await getFigmaDesignList(projectId);
      if (!initialResult?.data?.length) {
         setTimeout(() => {
        setFigmaLoader(false);
      }, 1500);
        return;
      }

      const fullFigmaId = initialResult.data[0].figma_id;
      const parts = fullFigmaId.split("-");
      const shortFigmaId = parts[parts.length - 1];
      // Step 2: Get detailed design info including screen list
      const designDetailResult = await getFigmaDesignList(projectId, fullFigmaId);
      const screenList = designDetailResult?.data?.screen_list || [];
      const proceedScreens = screenList.filter(screen => screen.processed === true);
      const screenIds = proceedScreens.map(screen => screen.screen_id);
      // Step 3: Get the image URLs by frame/screen ID
      const figmaJsonData = await getFigmaFilesScreens(projectId, shortFigmaId,screenIds); // { "9:680": "...", "9:739": "..." }
      // Step 4: Merge screen metadata with image URLs
      const combinedData = screenList
        .filter((screen) => figmaJsonData[screen.screen_id]) // Only include processed/available screens
        .map((screen) => ({
          screen_id: screen.screen_id,
          screen_name: screen.screen_name,
          image_url: figmaJsonData[screen.screen_id]
        }));

        setFigmaData(combinedData)

    } catch (error) {
      console.error("Error fetching Figma designs:", error);
    }finally{
      setTimeout(() => {
      setFigmaLoader(false);
    }, 1500);
    }
  };


  if (isDeepQuery) {
    return (
      <div className="inline-flex justify-start items-center gap-1">
        <button
          role="tab"
          aria-selected={activeTab === "Editor"}
          className={`h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Editor"
            ? "bg-orange-500 text-white"
            : "text-gray-700 hover:bg-gray-100"
            }`}
          onClick={() => toggleTab("Editor")}
        >
          <div className="text-sm font-medium font-['Inter'] leading-none">Editor</div>
        </button>
        {documentContent && (
          <button
            role="tab"
            aria-selected={activeTab === "Document"}
            className={`relative h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Document"
              ? "bg-orange-500 text-white"
              : "text-gray-700 hover:bg-gray-100"
              }`}
            onClick={() => toggleTab("Document")}
          >
            {activeTab === "Editor" && newDocAlert && (
              <div className="relative pb-0.5">
                <div className="absolute inset-0 w-2 h-2 bg-red-500 rounded-full animate-ping opacity-75"></div>
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              </div>
            )}
            <div className="text-sm font-medium font-['Inter'] leading-none">Documents</div>
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="inline-flex justify-start items-center gap-1">
      <button
        role="tab"
        aria-selected={activeTab === "Code"}
        className={`h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Code"
          ? "bg-orange-500 text-white"
          : "text-gray-700 hover:bg-gray-100"
          }`}
        onClick={() => toggleTab("Code")}
      >
        <div className="text-sm font-medium font-['Inter'] leading-none">Code</div>
      </button>
      <button
        role="tab"
        aria-selected={activeTab === "Preview"}
        className={`h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Preview"
          ? "bg-orange-500 text-white"
          : "text-gray-700 hover:bg-gray-100"
          }`}
        onClick={() => toggleTab("Preview")}
      >
        <div className="text-sm font-medium font-['Inter'] leading-none">Preview</div>
      </button>
      <button
        role="tab"
        aria-selected={activeTab === "Figma"}
        className={`h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Figma"
          ? "bg-orange-500 text-white"
          : "text-gray-700 hover:bg-gray-100"
          }`}
        onClick={handleFigmaTabClick}
      >
        <div className="text-sm font-medium font-['Inter'] leading-none">Figma</div>
      </button>
      {/* <button
        role="tab"
        aria-selected={activeTab === "Documents"}
        className={`h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Documents"
          ? "bg-orange-500 text-white"
          : "text-gray-700 hover:bg-gray-100"
          }`}
        onClick={() => toggleTab("Documents")}
      >
        <div className="text-sm font-medium font-['Inter'] leading-none">Documents</div>
      </button> */}
      {/* <button
        role="tab"
        aria-selected={activeTab === "Containers"}
        className={`h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Containers"
          ? "bg-orange-500 text-white"
          : "text-gray-700 hover:bg-gray-100"
          }`}
        onClick={() => toggleTab("Containers")}
      >
        <div className="text-sm font-medium font-['Inter'] leading-none">Containers</div>
      </button> */}
      {documentContent && (
        <button
          role="tab"
          aria-selected={activeTab === "Document"}
          className={`relative h-8 px-4 py-2 rounded-lg inline-flex justify-center items-center transition-all ${activeTab === "Document"
            ? "bg-orange-500 text-white"
            : "text-gray-700 hover:bg-gray-100"
            }`}
          onClick={() => toggleTab("Document")}
        >
          {(activeTab === "Preview" || activeTab === "Code") && newDocAlert && (
            <div className="relative pb-0.5">
              <div className="absolute inset-0 w-2 h-2 bg-red-500 rounded-full animate-ping opacity-75"></div>
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            </div>
          )}
          <div className="text-sm font-medium font-['Inter'] leading-none">Documents</div>
        </button>
      )}
    </div>
  );
});

TabButtons.displayName = 'TabButtons';

// Extract tab content into memoized component
const TabContent = React.memo(({
  currentTaskId,
  activeTab,
  documentContent,
  isSwaggerViewActive,
  formatWorkspaceUrl,
  currentIp,
  isPreviewLoading,
  previewError,
  handlePreviewRefresh,
  handlePortChange,
  portNumber,
  iframeKey,
  previewUrl,
  currentUrl,
  activeView,
  isLoading,
  setIsLoading,
  projectId,
  figmaData,
  figmaLoader
}) => {
  const isDeepQuery = currentTaskId?.startsWith("deep-query-job");

  if (isDeepQuery) {
    return (
      <div className="w-full h-full overflow-hidden bg-white">
        {documentContent && (
          <div
            className="p-2 w-full h-full"
            style={{
              display: activeTab === "Document" ? "block" : "none",
            }}
          >
            <DocumentsRenderPanel activeTab={activeTab} projectId={projectId} taskId={currentTaskId} />
          </div>
        )}
        <div
          className="p-2 w-full h-full"
          style={{
            display: activeTab === "Editor" ? "block" : "none",
          }}
        >
          <CodeViewPanel />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-hidden bg-white">
      <div
        className="p-2 w-full h-full"
        style={{
          display: activeTab === "Code" ? "block" : "none",
        }}
      >
        <CodeViewPanel />
      </div>
      {/* <div
        className="p-2 w-full h-full"
        style={{
          display: activeTab === "Documents" ? "block" : "none",
        }}
      >
        <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-700 mb-2">Documents Preview</h2>
            <p className="text-gray-500">Document content will be displayed here</p>
          </div>
        </div>
      </div> */}
      {/* <div
        className="p-2 w-full h-full"
        style={{
          display: activeTab === "Containers" ? "block" : "none",
        }}
      >
        <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-700 mb-2">Containers Preview</h2>
            <p className="text-gray-500">Container information will be displayed here</p>
          </div>
        </div>
      </div> */}
      <div
        className="p-2 w-full h-full"
        style={{
          display: activeTab === "Containers" ? "block" : "none",
        }}
      >
        <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-700 mb-2">Containers Preview</h2>
            <p className="text-gray-500">Container information will be displayed here</p>
          </div>
        </div>
      </div>
      <div
        className="p-2 w-full h-full"
        style={{
          display: activeTab === "Document" ? "block" : "none",
        }}
      >
        <DocumentsRenderPanel projectId={projectId} taskId={currentTaskId} />
      </div>
      {activeTab === "Preview" && (
        <div className=" w-full h-full bg-white overflow-hidden">
          <PreviewPanel
            currentTaskId={currentTaskId}
            currentIp={currentIp}
            formatWorkspaceUrl={formatWorkspaceUrl}
            portNumber={portNumber}
            onPortChange={handlePortChange}
          />
        </div>
      )}
      {activeTab === "Figma" && (
        <div className=" w-full h-full bg-white overflow-hidden">
          <FigmaPreviewPanel
            currentTaskId={currentTaskId}
            currentIp={currentIp}
            formatWorkspaceUrl={formatWorkspaceUrl}
            portNumber={portNumber}
            onPortChange={handlePortChange}
            showMode="figma"
            figmaData={figmaData}
            figmaLoader={figmaLoader}
          />
        </div>
      )}
    </div>
  );
});

TabContent.displayName = 'TabContent';

// Extract URL display section
const UrlDisplaySection = React.memo(({
  currentTaskId,
  activeTab,
  showUrl,
  handleRefresh,
  handleCopy,
  copied
}) => {
  const shouldShowUrl = (!currentTaskId?.startsWith("deep-query-job") || activeTab === "Editor");

  if (!shouldShowUrl) return null;

  if (activeTab === "Preview" || activeTab === "Figma") {
    return null
  }

  return (
    <div className="flex items-center bg-gray-100 rounded-md px-2 py-1 min-w-[300px] max-w-[50%] justify-between shadow-sm">
      <BootstrapTooltip title="Refresh" placement="bottom">
        <button
          className="p-0.5 text-gray-500 hover:text-gray-700"
          onClick={handleRefresh}
        >
          <RotateCw className="h-3 w-3" />
        </button>
      </BootstrapTooltip>
      <span className="text-gray-400 typography-caption mx-2 truncate">
        {showUrl}
      </span>
      <BootstrapTooltip
        title={copied ? "Copied!" : "Copy URL"}
        placement="bottom"
      >
        <button
          className={`p-0.5 ${copied
            ? "text-green-600 hover:bg-green-50"
            : "text-gray-500 hover:bg-gray-100 hover:text-gray-700"
            }`}
          onClick={handleCopy}
        >
          <Link2 className="h-3 w-3" />
        </button>
      </BootstrapTooltip>
    </div>
  );
});

UrlDisplaySection.displayName = 'UrlDisplaySection';

// Extract action buttons section
const TabActionButtons = React.memo(({
  currentTaskId,
  activeTab,
  showUrl,
  activeView,
  setActiveView,
  isSwaggerViewActive,
  toggleSwaggerView,
  isFullscreen,
  toggleFullscreen
}) => {
  const shouldShowUrlButtons = (!currentTaskId?.startsWith("deep-query-job") ||
    activeTab === "Code" ||
    activeTab === "Editor");

  return (
    <div className="flex items-center" style={{ backgroundColor: '#ffffff', opacity: 1 }}>

      {(shouldShowUrlButtons && (activeTab !== "Preview" && activeTab !== "Figma")) && (
        <>
          <button
            className="p-0.5 text-gray-500 hover:text-gray-700"
            onClick={() => window.open(showUrl, "_blank")}
          >
            <svg
              width="20px"
              height="20px"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.5 17C4.0875 17 3.73437 16.8541 3.44062 16.5604C3.14687 16.2666 3 15.9135 3 15.501V4.501C3 4.0885 3.14687 3.73537 3.44062 3.44162C3.73437 3.14787 4.0875 3.001 4.5 3.001H10V4.501H4.5V15.501H15.5V10.001H17V15.501C17 15.9135 16.8531 16.2666 16.5594 16.5604C16.2656 16.8541 15.9125 17 15.5 17H4.5ZM8.0625 13.001L7 11.9385L14.4375 4.501H12V3.001H17V8.001H15.5V5.5635L8.0625 13.001Z"
                fill="#4B5563"
              />
            </svg>
          </button>
          {activeTab === "Preview" && (
            <>
              <button
                className="p-0.5 text-gray-500 hover:text-gray-700 ml-1"
                onClick={() =>
                  setActiveView(
                    activeView === "console"
                      ? "browser"
                      : "console"
                  )
                }
              >
                <svg
                  width="20px"
                  height="20px"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2 16V14H4V5.5C4 5.0875 4.14687 4.73438 4.44062 4.44063C4.73438 4.14688 5.0875 4 5.5 4H17V5.5H5.5V14H10V16H2ZM12.7558 16C12.5436 16 12.3646 15.9282 12.2188 15.7846C12.0729 15.641 12 15.463 12 15.2506V7.75604C12 7.54368 12.0718 7.36458 12.2154 7.21875C12.359 7.07292 12.5369 7 12.7492 7H17.2442C17.4564 7 17.6354 7.07181 17.7812 7.21542C17.9271 7.35903 18 7.53701 18 7.74937V15.244C18 15.4563 17.9282 15.6354 17.7846 15.7813C17.641 15.9271 17.4631 16 17.2508 16H12.7558ZM13.5 14H16.5V8.5H13.5V14Z"
                    fill="#4B5563"
                  />
                </svg>
              </button>
              <BootstrapTooltip
                title="Swagger View"
                placement="bottom"
              >
                <button
                  className={`p-0.5 ml-1 ${isSwaggerViewActive
                    ? "text-primary"
                    : "text-gray-500 hover:text-gray-700"
                    }`}
                  onClick={toggleSwaggerView}
                >
                  <BookOpen size={20} />
                </button>
              </BootstrapTooltip>
            </>
          )}
        </>
      )}
      <BootstrapTooltip
        title={
          isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"
        }
        placement="bottom"
      >
        <button
          className="p-0.5 text-gray-500 hover:text-gray-700 ml-1"
          onClick={toggleFullscreen}
        >
          {isFullscreen ? (
            <Minimize size={20} />
          ) : (
            <Maximize size={20} />
          )}
        </button>
      </BootstrapTooltip>
    </div>
  );
});

TabActionButtons.displayName = 'TabActionButtons';

// Add state reducer for related UI states to prevent cascading re-renders
const uiStateReducer = (state, action) => {
  switch (action.type) {
    case 'SET_TAB_AND_URL':
      return {
        ...state,
        activeTab: action.activeTab,
        showUrl: action.showUrl,
      };
    case 'SET_PREVIEW_STATE':
      return {
        ...state,
        isPreviewLoading: action.isPreviewLoading,
        previewError: action.previewError,
        previewUrl: action.previewUrl,
      };
    case 'SET_URL_STATE':
      return {
        ...state,
        currentUrl: action.currentUrl,
        showUrl: action.showUrl,
      };
    case 'BATCH_UPDATE':
      return {
        ...state,
        ...action.updates,
      };
    default:
      return state;
  }
};

const CodeGenerationModal = () => {
  const {
    isVisible,
    tab,
    isReady,
    closeModal,
    currentIframeUrl,
    notification,
    activeTab,
    setActiveTab,
    setSelectedFile,
    architectureId,
    sessionName,
    setSessionName,
    fetchCurrentUrl,
    currentIp,
    steps,
    wsStatus,
    formatWorkspaceUrl,
    wsConnection,
    setStatusData,
    newDocAlert,
    llmModel,
    setLlmModel,
    activeView,
    setActiveView,
    documentContent,
    taskDetails,
    timeoutWarning,
    refreshCodeEditor,
    containers,
    selectedContainer,
    setSelectedContainer,
    isAiTyping,
    sessionLimitModal,
    setSessionLimitModal,
    figmaData,
    setFigmaData,
    setFigmaLoader,
    figmaLoader
  } = useCodeGeneration();

  const { showPlanRestriction, creditLimitCrossed } = usePlanRestriction();

  // Use reducer for related UI states to prevent cascading re-renders
  const [uiState, dispatchUiState] = useReducer(uiStateReducer, {
    currentUrl: "",
    showUrl: null,
    isPreviewLoading: true,
    previewError: null,
    previewUrl: null,
  });

  // Individual states that don't cascade
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [iframeKey, setIframeKey] = useState(Date.now());
  const [portNumber, setPortNumber] = useState(3000);
  const [isCodeInitialized, setIsCodeInitialized] = useState(true);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isCodeViewLoaded, setCodeViewLoaded] = useState(false);
  const [isPanelExpanded, setIsPanelExpanded] = useState(false);
  const params = useParams();
  const { projectId } = params;
  const searchParams = useSearchParams();
  const router = useRouter();
  const currentTaskId = searchParams.get("task_id");
  const { showAlert } = useContext(AlertContext);
  const [isRefreshingPlan, setIsRefreshingPlan] = useState(false);

  // Add minimal status tracking
  const [currentTaskStatus, setCurrentTaskStatus] = useState(null);


  const [isUpdating, setIsUpdating] = useState(false);
  const [isTerminating, setIsTerminating] = useState(false);
  const [isMerging, setIsMerging] = useState(false);
  const isFirstRender = useRef(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { isFullscreen, toggleFullscreen, rightPanelRef } = useFullscreenHandler();
  const [isTaskProgressOpen, setIsTaskProgressOpen] = useState(false);
  const deployPanelRef = useRef(null);
  const [showDeploymentsModal, setShowDeploymentsModal] = useState(false);
  const [deployments, setDeployments] = useState([]);
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(false);

  // Add container details panel state
  const [showContainerDetailsPanel, setShowContainerDetailsPanel] = useState(false);
  const [selectedContainerForDeployment, setSelectedContainerForDeployment] = useState(null);
  const [isLoadingManifest, setIsLoadingManifest] = useState(false);

  const [availableModels, setAvailableModels] = useState([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [isSwaggerViewActive, setIsSwaggerViewActive] = useState(false);
  const [terminateTimeoutId, setTerminateTimeoutId] = useState(null);
  const [isMergeAndTerminate, setIsMergeAndTerminate] = useState(false);
  const [isHandlingStopMessage, setIsHandlingStopMessage] = useState(false);
  const tenantId = Cookies.get('tenant_id');
  const [subscriptionData, setSubscriptionData] = useState({
    currentPlan: 'Loading...',
    planCredits: null,
    organizationCost: 0
  });
  // Use a ref to store the last user input for session name
  const lastSessionInputRef = useRef(sessionName || "Untitled");

  // Initialize with sessionName if it exists, otherwise "Untitled"
  const [sessionInputValue, setSessionInputValue] = useState(sessionName || "Untitled");
  const [selectedOption, setSelectedOption] = useState("save");
  const [actionLoading, setActionLoading] = useState(false);
  const [showMoreDropdown, setShowMoreDropdown] = useState(false);
  

  // Update sessionInputValue when sessionName changes
  useEffect(() => {
    if (sessionName && sessionName !== "Untitled") {
      setSessionInputValue(sessionName);
      lastSessionInputRef.current = sessionName;
    }
  }, [sessionName]);

  // Update sessionInputValue when modal opens to preserve user input
  useEffect(() => {
    if (showConfirmModal) {
      // Use the last user input if available, otherwise fall back to sessionName or "Untitled"
      const valueToUse = lastSessionInputRef.current !== "Untitled"
        ? lastSessionInputRef.current
        : (sessionName || "Untitled");
      setSessionInputValue(valueToUse);
    }
  }, [showConfirmModal, sessionName]);

  // Initialize lastSessionInputRef when sessionName changes from context
  useEffect(() => {
    if (sessionName && sessionName !== "Untitled") {
      lastSessionInputRef.current = sessionName;
    }
  }, [sessionName]);

  // Add selected container state
  const [selectedContainerFromDropdown, setSelectedContainerFromDropdown] = useState(null);

  // Add container loading state
  const [isContainersLoading, setIsContainersLoading] = useState(true);

  // Use real containers from context, don't fallback to sample data
  const availableContainers = containers || [];

  // Reset selectedOption to "save" whenever modal opens
  useEffect(() => {
    if (showConfirmModal) {
      setSelectedOption("save");
    }
  }, [showConfirmModal]);

  // Use refs for values that don't need to trigger re-renders
  const currentUrlRef = useRef(uiState.currentUrl);
  const showUrlRef = useRef(uiState.showUrl);

  // Update refs when state changes (for accessing current values in callbacks)
  useEffect(() => {
    currentUrlRef.current = uiState.currentUrl;
    showUrlRef.current = uiState.showUrl;
  }, [uiState.currentUrl, uiState.showUrl]);

  // Add deployment context
  const {
    isDeploymentConfigOpen,
    setIsDeploymentConfigOpen,
    isDeployPanelOpen,
    setIsDeployPanelOpen,
    setWsConnection: setDeploymentWsConnection,
    handleDeployClick,
    showDeploymentSuccess,
    setShowDeploymentSuccess,
    deployedUrl,
    fetchRepositories,
    isDeploying,
    setIsDeploying,
    currentRepository,
    manifest,
    fetchManifest,
  } = useDeployment();

  // Add tasks state
  const [tasks, setTasks] = useState([]);
  // Add state to track if deployment history was just opened automatically
  const [justOpenedDeploymentHistory, setJustOpenedDeploymentHistory] = useState(false);

  // Memoize expensive computations to prevent unnecessary re-renders
  const hasValidUrl = useMemo(() => {
    return uiState.currentUrl &&
      uiState.currentUrl !== "No URL found" &&
      uiState.currentUrl !== "";
  }, [uiState.currentUrl]);

  const isDeepQuery = useMemo(() => {
    return currentTaskId?.startsWith("deep-query-job");
  }, [currentTaskId]);

  const isCodeMaintenance = useMemo(() => {
    return currentTaskId?.startsWith("cm");
  }, [currentTaskId]);

  // Memoize header generation to prevent re-creation on every render
  const getHeadersRaw = useCallback(() => {
    const idToken = Cookies.get("idToken");
    const tenant_id = Cookies.get("tenant_id");

    return {
      Authorization: `Bearer ${idToken}`,
      "Content-Type": "application/json",
      "X-Tenant-ID": tenant_id,
    };
  }, []);

  // Debounced URL updates to prevent cascading re-renders
  const debouncedUrlUpdate = useCallback(
    debounce((newUrl, shouldUpdateShowUrl = false) => {
      dispatchUiState({
        type: 'BATCH_UPDATE',
        updates: {
          currentUrl: newUrl,
          ...(shouldUpdateShowUrl && { showUrl: newUrl }),
        }
      });
    }, 300),
    []
  );

  // Removed subscription fetching - will be handled globally
  const fetchCurrentPlan = async () => {

    try {

      const idToken = Cookies.get("idToken");
      const userData = decryptToken(idToken);
      const userId = userData.sub

      if (tenantId === 'b2c' && userId) {
        const subscription = await getLatestSubscription(userId);

        if (subscription && subscription.price_id) {
          const cleanedCost = subscription.current_cost
            ? typeof subscription.current_cost === 'string'
              ? parseFloat(subscription.current_cost.replace('$', '').trim())
              : parseFloat(subscription.current_cost)
            : 0
          // Now we can directly use the fields from the enhanced endpoint response
          setSubscriptionData({
            currentPlan: subscription.product_name,
            planCredits: subscription.credits || 50000, //50000 is for free
            organizationCost: cleanedCost
          });
        }
        else {
          setSubscriptionData({
            currentPlan: 'Free',
            planCredits: 50000,
            organizationCost: 0
          });
        }
      }
    } catch (error) {

      setSubscriptionData({
        currentPlan: 'Free',
        planCredits: 50000,
        organizationCost: 0
      });
      // showAlert("There was an error loading your subscription data. Defaulting to Free plan.", "error");
    } finally {

    }
  };
  useEffect(() => {
    fetchCurrentPlan()
  }, [searchParams, currentTaskId, projectId, tenantId])

  const handleRefreshPlan = async () => {
    setIsRefreshingPlan(true);
    await fetchCurrentPlan();
    // Show loading for at least 1 second
    setTimeout(() => {
      setIsRefreshingPlan(false);
    }, 1000);
  };

  // Consolidated tab switching logic to prevent cascading re-renders
  const toggleTab = useCallback((label) => {
    setActiveTab(label);

    // Batch the tab and URL state update
    let newShowUrl = null;

    if (isDeepQuery) {
      if (label === "Editor") {
        newShowUrl = currentIframeUrl;
      }
    } else {
      if (label === "Code") {
        newShowUrl = currentIframeUrl;
      } else if (label === "Preview") {
        newShowUrl = uiState.previewUrl || uiState.currentUrl;
      }
    }

    // Batch update to prevent cascading re-renders
    dispatchUiState({
      type: 'SET_TAB_AND_URL',
      activeTab: label,
      showUrl: newShowUrl,
    });
  }, [isDeepQuery, currentIframeUrl, uiState.previewUrl, uiState.currentUrl, setActiveTab]);

  // Initialize tasks when steps change - memoized to prevent unnecessary re-renders
  const formattedTasks = useMemo(() => {
    if (!steps) return [];

    return steps.map((step, index) => ({
      id: index + 1,
      title: step.name || `Task ${index + 1}`,
      description: step.description || "",
      status: step.status || "pending",
    }));
  }, [steps]);

  useEffect(() => {
    setTasks(formattedTasks);
  }, [formattedTasks]);

  const openSidebar = useCallback(() => {
    setIsSidebarOpen(true);
  }, []);

  const closeSidebar = useCallback(() => {
    setIsSidebarOpen(false);
  }, []);

  // Add cleanup refs to track active connections and timers
  const activeTimersRef = useRef(new Set());
  const eventSourceRef = useRef(null);
  const isComponentMountedRef = useRef(true);

  // Enhanced cleanup utility function
  const addTimerToCleanup = useCallback((timerId) => {
    activeTimersRef.current.add(timerId);
  }, []);

  const clearAllActiveTimers = useCallback(() => {
    activeTimersRef.current.forEach(timerId => {
      clearTimeout(timerId);
      clearInterval(timerId);
    });
    activeTimersRef.current.clear();
  }, []);

  // Safe async operations utility
  const safeAsyncOperation = useCallback((asyncFn) => {
    return (...args) => {
      if (isComponentMountedRef.current) {
        return asyncFn(...args);
      }
    };
  }, []);

  // Handle merge operation
  const handleMerge = useCallback(async () => {
    if (!currentTaskId) {
      showAlert("Unable to execute merge command - no task ID", "error");
      setIsMerging(false);
      setIsMergeAndTerminate(false);
      return;
    }

    setActionLoading(true);
    setIsMerging(true);
    setIsMergeAndTerminate(true);

    try {
      // Call the merge API endpoint
      await mergeToKaviaMain(currentTaskId);

      // If merge was successful, send stop command via WebSocket
      if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
        wsConnection.send(JSON.stringify({
          type: "stop",
          task_id: currentTaskId,
        }));
      }

      // Update task name in background
      const finalSessionName = sessionInputValue.trim() || "Untitled";
      updateTask(currentTaskId, { session_name: finalSessionName })
        .then(() => {
          if (isComponentMountedRef.current) {
            setSessionName(finalSessionName);
            lastSessionInputRef.current = finalSessionName;
          }
        })
        .catch((error) => {
          console.error("Background task update failed:", error);
        });

      // Reset deployment context states
      setIsDeploymentConfigOpen(false);
      setIsDeployPanelOpen(false);
      setIsDeploying(false);
      setShowDeploymentSuccess(false);
      setDeploymentWsConnection(null);

      // Reset loading states
      setActionLoading(false);
      setIsMerging(false);
      setIsMergeAndTerminate(false);
      setShowConfirmModal(false);

      // Close the modal
      closeModal();

      showAlert("Session saved and closed successfully", "success");

      // Redirect to code maintenance page using the same pattern as handleTabChange
      const { organization_id, type } = params;
      const newSearchParams = new URLSearchParams();
      newSearchParams.set('tab', 'maintenance');
      const maintenanceUrl = `/${organization_id}/${type}/${projectId}/code?${newSearchParams.toString()}`;

      // Use setTimeout to ensure the modal is fully closed before navigation
      setTimeout(() => {
        router.push(maintenanceUrl);
      }, 100);

    } catch (error) {
      console.error("Merge operation error:", error);

      if (isComponentMountedRef.current) {
        setActionLoading(false);
        setIsMerging(false);
        setIsMergeAndTerminate(false);
        showAlert("Couldn't merge to kavia main. Try later.", "error");
      }
    }
  }, [currentTaskId, showAlert, sessionInputValue, setSessionName, setIsDeploymentConfigOpen, setIsDeployPanelOpen, setIsDeploying, setShowDeploymentSuccess, setDeploymentWsConnection, closeModal, params, projectId, router]);

  // Consolidated URL and preview management to prevent cascading updates
  useEffect(() => {
    if (!currentIp || !portNumber || !currentIframeUrl) return;

    const newCurrentUrl = getPreviewUrlFromBackend(currentIframeUrl, portNumber);
    const newPreviewUrl = getPreviewUrlFromBackend(currentIframeUrl, 3001);

    // Batch all URL-related updates
    dispatchUiState({
      type: 'BATCH_UPDATE',
      updates: {
        currentUrl: newCurrentUrl,
        previewUrl: newPreviewUrl,
        isPreviewLoading: false,
      }
    });
  }, [currentIframeUrl, currentIp, portNumber]);

  // Consolidated URL display logic - only update when necessary
  useEffect(() => {
    let newShowUrl = null;

    if (isDeepQuery) {
      if (activeTab === "Editor") {
        newShowUrl = currentIframeUrl;
      }
    } else {
      if (activeTab === "Code") {
        newShowUrl = currentIframeUrl;
      } else if (activeTab === "Preview") {
        newShowUrl = uiState.previewUrl || uiState.currentUrl;
      }
    }

    // Only update if the URL actually changed
    if (newShowUrl !== uiState.showUrl) {
      dispatchUiState({
        type: 'BATCH_UPDATE',
        updates: { showUrl: newShowUrl }
      });
    }
  }, [activeTab, currentIframeUrl, uiState.previewUrl, uiState.currentUrl, isDeepQuery, uiState.showUrl]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      isComponentMountedRef.current = false;
      clearAllActiveTimers();

      // Cleanup terminate timeout if exists
      if (terminateTimeoutId) {
        clearTimeout(terminateTimeoutId);
      }

      // Cleanup EventSource
      if (eventSourceRef.current) {
        eventSourceRef.current.abort();
        eventSourceRef.current = null;
      }
    };
  }, [clearAllActiveTimers, terminateTimeoutId]);

  // Removed EventSource connection - using WebSocket only
  useEffect(() => {
    if (!currentTaskId) return;

    if (isDeepQuery) {
      // Set Editor as the default active tab for deep analysis
      toggleTab("Editor");
    }
  }, [currentTaskId, isDeepQuery, toggleTab]);

  // Initial URL fetch and status initialization - only when modal becomes visible
  useEffect(() => {
    if (isVisible) {
      fetchCurrentUrl();
    }
  }, [isVisible, fetchCurrentUrl]);

  // Initialize status from taskDetails when available
  useEffect(() => {
    if (taskDetails?.status && !currentTaskStatus) {
      setCurrentTaskStatus(taskDetails.status);
    }
  }, [taskDetails?.status, currentTaskStatus]);

  // Removed URL status checking - not needed for basic functionality

  // Enhanced refresh handler with timer cleanup
  const handleRefresh = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    // Update iframe key to force re-render for preview
    setIframeKey(Date.now());

    // Also refresh the code editor
    if (refreshCodeEditor) {
      refreshCodeEditor();
    }
  }, [portNumber, refreshCodeEditor]);

  // Enhanced preview refresh handler with timer cleanup
  const handlePreviewRefresh = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    dispatchUiState({
      type: 'SET_PREVIEW_STATE',
      isPreviewLoading: true,
      previewError: null,
    });

    setIsSwaggerViewActive(false);
    setIframeKey(Date.now());

    // Simulate refresh delay with proper cleanup
    const timerId = setTimeout(() => {
      if (isComponentMountedRef.current) {
        dispatchUiState({
          type: 'BATCH_UPDATE',
          updates: { isPreviewLoading: false }
        });
      }
      activeTimersRef.current.delete(timerId);
    }, 1000);

    addTimerToCleanup(timerId);
  }, [addTimerToCleanup]);

  // Enhanced copy handler with timer cleanup
  const handleCopy = useCallback(async () => {
    try {
      if (!uiState.showUrl || !isComponentMountedRef.current) return;
      await navigator.clipboard.writeText(uiState.showUrl);

      if (isComponentMountedRef.current) {
        setCopied(true);
        const timerId = setTimeout(() => {
          if (isComponentMountedRef.current) {
            setCopied(false);
          }
          activeTimersRef.current.delete(timerId);
        }, 2000);
        addTimerToCleanup(timerId);
      }
    } catch (error) {
      if (isComponentMountedRef.current) {
        alert("Failed to copy URL");
      }
    }
  }, [uiState.showUrl, addTimerToCleanup]);

  // Add download logs function
  const handleDownloadLogs = useCallback(async () => {
    if (!currentTaskId || !projectId) {
      showAlert("Missing task or project information", "error");
      return;
    }

    const tenant_id = Cookies.get("tenant_id");
    if (!tenant_id) {
      showAlert("Tenant information not found", "error");
      return;
    }

    try {
      showAlert("Preparing logs download...", "info");

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/manage/super/download_logs_CGA/${tenant_id}/${projectId}/${currentTaskId}`,
        {
          method: 'GET',
          headers: getHeadersRaw(),
        }
      );

      // Handle different HTTP status codes properly
      if (!response.ok) {
        let errorMessage = "Failed to download logs";

        try {
          // Try to parse error response as JSON first
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } catch {
          // If JSON parsing fails, use response status text
          errorMessage = response.statusText || errorMessage;
        }

        // Handle specific status codes with user-friendly messages
        switch (response.status) {
          case 404:
            showAlert(`No logs found for task ${currentTaskId}`, "error");
            break;
          case 403:
            showAlert("You are not authorized to download logs for this tenant", "error");
            break;
          case 500:
            if (errorMessage.includes("No allowed tenants configured")) {
              showAlert("Log download feature is not configured. Contact administrator", "error");
            } else {
              showAlert(`Server error: ${errorMessage}`, "error");
            }
            break;
          case 401:
            showAlert("Authentication required. Please log in again", "error");
            break;
          case 429:
            showAlert("Too many requests. Please try again later", "error");
            break;
          default:
            showAlert(`Error (${response.status}): ${errorMessage}`, "error");
        }
        return;
      }

      // Process successful response
      const blob = await response.blob();

      // Validate blob size
      if (blob.size === 0) {
        showAlert("Downloaded file is empty. No logs available", "warning");
        return;
      }

      // Extract filename from Content-Disposition header if available
      let filename = `logs-${currentTaskId}-${new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]}.zip`;
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create and trigger download
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      downloadLink.style.display = 'none';
      downloadLink.href = url;
      downloadLink.download = filename;

      // Add to DOM, click, and cleanup
      document.body.appendChild(downloadLink);
      downloadLink.click();

      // Cleanup
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(downloadLink);
      }, 100);

      showAlert(`Logs downloaded successfully: ${filename}`, "success");

    } catch (error) {
      console.error("Download logs error:", error);

      // Handle network and other errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        showAlert("Network error. Please check your connection and try again", "error");
      } else if (error.name === 'AbortError') {
        showAlert("Download was cancelled", "warning");
      } else {
        showAlert(`Unexpected error: ${error.message}`, "error");
      }
    }
  }, [currentTaskId, projectId, getHeadersRaw, showAlert]);


  const handlePortChange = useCallback((event) => {
    setPortNumber(Number(event.target.value));
  }, []);

  // Enhanced modal close handlers with proper cleanup
  const handleConfirmClose = useCallback(() => {
    // Close any open deployment interfaces first
    setIsDeploymentConfigOpen(false);
    setIsDeployPanelOpen(false);
    setShowDeploymentSuccess(false);
    setIsDeploying(false);

    // Set the default option before showing the modal
    setSelectedOption("save");
    setShowConfirmModal(true);

    if (sessionStorage.getItem("isCodeMaintenance")) {
      sessionStorage.removeItem("isCodeMaintenance");
      sessionStorage.removeItem("selectedRepos");
    }
  }, [setIsDeploymentConfigOpen, setIsDeployPanelOpen, setShowDeploymentSuccess, setIsDeploying]);

  const confirmCloseModal = useCallback(async () => {
    console.log("confirmCloseModal called - starting continue session");
    setActionLoading(true);
    setIsUpdating(true);

    // Set a timeout to ensure the action completes within 5 seconds
    const timeoutId = setTimeout(() => {
      if (isComponentMountedRef.current) {
        console.warn("Continue session timeout (5s) - automatically closing modal");

        // Force reset all loading states
        setActionLoading(false);
        setIsUpdating(false);
        setShowConfirmModal(false);

        // Add a small delay to ensure state updates are processed
        setTimeout(() => {
          if (isComponentMountedRef.current) {
            closeModal();
          }
        }, 100);

        showAlert("Session closed - operation timed out", "warning");
      }
      activeTimersRef.current.delete(timeoutId);
    }, 5000); // 5 second timeout

    addTimerToCleanup(timeoutId);

    try {
      const finalSessionName = sessionInputValue.trim() || "Untitled";

      // Try to update the task, but don't let it block the modal closing
      try {
        await updateTask(currentTaskId, { session_name: finalSessionName });
        if (isComponentMountedRef.current) {
          setSessionName(finalSessionName);
          lastSessionInputRef.current = finalSessionName;
        }
      } catch (updateError) {
        console.warn("Failed to update session name:", updateError);
        // Continue with closing the modal even if update fails
      }

      // Clear the timeout since operation completed successfully
      clearTimeout(timeoutId);
      activeTimersRef.current.delete(timeoutId);

      if (isComponentMountedRef.current) {
        // Reset deployment context states
        setIsDeploymentConfigOpen(false);
        setIsDeployPanelOpen(false);
        setIsDeploying(false);
        setShowDeploymentSuccess(false);
        setDeploymentWsConnection(null);

        console.log("confirmCloseModal - closing modal");
        setShowConfirmModal(false);

        // Add a small delay to ensure state updates are processed
        setTimeout(() => {
          if (isComponentMountedRef.current) {
            closeModal();
          }
        }, 100);
      }
    } catch (error) {
      console.error("Continue session error:", error);

      // Clear the timeout on error
      clearTimeout(timeoutId);
      activeTimersRef.current.delete(timeoutId);

      if (isComponentMountedRef.current) {
        showAlert("Failed to continue session", "error");
        // Close modal even on error
        setShowConfirmModal(false);

        // Add a small delay to ensure state updates are processed
        setTimeout(() => {
          if (isComponentMountedRef.current) {
            closeModal();
          }
        }, 100);
      }
    } finally {
      if (isComponentMountedRef.current) {
        setIsUpdating(false);
        setActionLoading(false);
      }
    }
  }, [sessionInputValue, currentTaskId, setSessionName, showAlert, setIsDeploymentConfigOpen, setIsDeployPanelOpen, setIsDeploying, setShowDeploymentSuccess, setDeploymentWsConnection, addTimerToCleanup, closeModal]);

  const handleStopAndClose = useCallback(async () => {
    setActionLoading(true);
    setIsTerminating(true);

    try {
      const finalSessionName = sessionInputValue.trim() || "Untitled";

      // Call discard and exit API in parallel with WebSocket stop
      if (projectId) {
        await discardAndExit(projectId);
      }

      // Send stop message through websocket if connected
      if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
        wsConnection.send(JSON.stringify({
          type: "stop",
          task_id: currentTaskId,
        }));

        // Wait 2 seconds before closing the modal
        const timeoutId = setTimeout(() => {
          if (isComponentMountedRef.current) {
            // Reset all states
            setIsTerminating(false);
            setActionLoading(false);
            setShowConfirmModal(false);

            // Reset deployment context states
            setIsDeploymentConfigOpen(false);
            setIsDeployPanelOpen(false);
            setIsDeploying(false);
            setShowDeploymentSuccess(false);
            setDeploymentWsConnection(null);

            // Update task name in background without waiting
            updateTask(currentTaskId, { session_name: finalSessionName, status: "stopped" })
              .catch(console.error);

            // Close modal after 2 seconds
            closeModal();
          }
        }, 2000);

        setTerminateTimeoutId(timeoutId);
        addTimerToCleanup(timeoutId);

        return;
      }

      // If no WebSocket, still wait 2 seconds before closing
      const timeoutId = setTimeout(() => {
        if (isComponentMountedRef.current) {
          setIsTerminating(false);
          setActionLoading(false);
          setShowConfirmModal(false);
          closeModal();
        }
      }, 2000);

      addTimerToCleanup(timeoutId);

    } catch (error) {
      console.error("Stop session error:", error);
      // Always close the modal even on error
      if (isComponentMountedRef.current) {
        showAlert("Failed to process your request", "error");
        setIsTerminating(false);
        setActionLoading(false);
        setShowConfirmModal(false);
        closeModal();
      }
    } finally {
      // Navigate to the code page without task_id
      if (params?.organization_id && projectId) {
        const organizationId = params.organization_id;
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete("task_id");

        router.push(`/${organizationId}/project/${projectId}/code?${newSearchParams.toString()}`);
      }
    }

  }, [
    sessionInputValue,
    currentTaskId,
    wsConnection,
    setIsDeploymentConfigOpen,
    setIsDeployPanelOpen,
    setIsDeploying,
    setShowDeploymentSuccess,
    setDeploymentWsConnection,
    closeModal,
    params,
    projectId,
    searchParams,
    router
  ]);
  // Consolidate file selection and tab effects
  useEffect(() => {
    if (activeTab !== "File Watch" && setSelectedFile) {
      setSelectedFile(null);
    }
  }, [activeTab, setSelectedFile]);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    if (!showPlanRestriction && creditLimitCrossed) {
      handleStopAndClose();
    }
  }, [showPlanRestriction, creditLimitCrossed, handleStopAndClose]);

  // Code initialization effects
  useEffect(() => {
    if (tab === "Code" && !isCodeInitialized) {
      setIsCodeInitialized(true);
    }
  }, [tab, isCodeInitialized]);

  useEffect(() => {
    if (activeTab === "Code" && !isCodeViewLoaded) {
      setCodeViewLoaded(true);
    }
  }, [activeTab, isCodeViewLoaded]);

  // Removed live status checking - not needed for basic functionality

  console.log("CodeGenerationModal render - isVisible:", isVisible);
  if (!isVisible) return null;

  // Add this helper function before the return statement
  const isCodeOrPreview = (tab) => tab === "Code" || tab === "Preview";

  const TaskProgresstext = ({ completedTasks, totalTasks }) => {
    // Show loading spinner only when there are tasks in progress (not completed and not pending)
    const hasActiveTask = steps && steps.some(step =>
      step.status === "in-progress" ||
      step.status === "in_progress" ||
      step.status === "running"
    );

    return (
      <div className="px-2 py-1 bg-primary-50 rounded-md inline-flex items-center gap-1.5 cursor-pointer">
        {hasActiveTask && (
          <Loader2 className="w-3 h-3 animate-spin text-primary-600 flex-shrink-0" />
        )}
        <div className="text-gray-800 text-xs font-medium font-['Inter'] leading-none whitespace-nowrap">
          {completedTasks}/{totalTasks} Tasks
        </div>
      </div>
    );
  };

  // Deploy panel click outside handler with deployment context
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        deployPanelRef.current &&
        !deployPanelRef.current.contains(event.target)
      ) {
        setIsDeployPanelOpen(false);
      }
    };

    if (isDeployPanelOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDeployPanelOpen, setIsDeployPanelOpen]);

  // Fetch repositories when component mounts - now handled by the deployment context
  useEffect(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && isDeployPanelOpen) {
      fetchRepositories();
    }
  }, [wsConnection, isDeployPanelOpen, fetchRepositories]);

  // Add this function to handle showing deployments
  const handleShowDeployments = () => {
    setShowDeploymentsModal(!showDeploymentsModal);
    if (!showDeploymentsModal) {
      fetchDeployments();
    }
  };

  // Add this function to handle showing container details
  const handleShowContainerDetails = () => {
    setShowContainerDetailsPanel(!showContainerDetailsPanel);

    // If opening the panel and we don't have manifest data, fetch it
    if (!showContainerDetailsPanel && !manifest) {
      setIsLoadingManifest(true);
      fetchManifest();
    }
  };

  // Add this function to handle container selection for deployment
  const handleContainerSelect = (container) => {
    setSelectedContainerForDeployment(container);
    setShowContainerDetailsPanel(false);

    // Update query parameters with container type
    const currentParams = new URLSearchParams(searchParams);
    currentParams.set('type', container.type);

    // Update the URL with the new query parameter
    window.history.replaceState(
      null,
      '',
      `${window.location.pathname}?${currentParams.toString()}`
    );

    // Open the deployment interface with the selected container
    setIsDeploymentConfigOpen(true);
  };

  // Add click outside handler for deployments dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Don't close if the deployment config modal is open (to prevent interference)
      if (isDeploymentConfigOpen) {
        return;
      }

      // Add a delay to prevent interference with automatic opening
      setTimeout(() => {
        // Don't close deployment history if it was just opened automatically
        if (
          showDeploymentsModal &&
          !event.target.closest(".deployments-dropdown") &&
          !justOpenedDeploymentHistory
        ) {
          setShowDeploymentsModal(false);
        }

        // Also handle container details panel
        if (
          showContainerDetailsPanel &&
          !event.target.closest(".deployments-dropdown")
        ) {
          setShowContainerDetailsPanel(false);
        }

        // Handle More dropdown
        if (
          showMoreDropdown &&
          !event.target.closest(".more-dropdown")
        ) {
          setShowMoreDropdown(false);
        }
      }, 500);
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDeploymentsModal, showContainerDetailsPanel, showMoreDropdown, isDeploymentConfigOpen, justOpenedDeploymentHistory]);

  // Add this function to fetch deployments
  const fetchDeployments = async () => {
    setIsLoadingDeployments(true);
    try {
      const response = await listDeployments(projectId);
      // Sort deployments by created_at in descending order (latest first)
      const sortedDeployments = response.sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      );
      setDeployments(sortedDeployments);
    } catch (error) {
      showAlert("Failed to fetch deployments", "error");
    } finally {
      setIsLoadingDeployments(false);
    }
  };

  // Add delete deployment function
  const handleDeleteDeployment = async (deploymentId, appId) => {
    try {
      await deleteDeployment(projectId, appId);
      showAlert("Deployment deleted successfully", "success");
      // Refresh deployments list
      fetchDeployments();
    } catch (error) {
      showAlert("Failed to delete deployment", "error");
    }
  };

  // Add confirmation dialog state
  const [deleteConfirmation, setDeleteConfirmation] = useState(null);

  // Share the WebSocket connection with the deployment context
  useEffect(() => {
    if (wsConnection) {
      setDeploymentWsConnection(wsConnection);
    }
  }, [wsConnection, setDeploymentWsConnection]);

  // Fetch manifest when modal opens and WebSocket is ready
  useEffect(() => {
    if (isVisible && wsConnection?.readyState === WebSocket.OPEN && currentTaskId && fetchManifest) {
      fetchManifest();
    }
  }, [isVisible, wsConnection, currentTaskId, fetchManifest]);

  // Reset manifest loading state when manifest is received
  useEffect(() => {
    if (manifest) {
      setIsLoadingManifest(false);
    }
  }, [manifest]);

  // Handle container loading state based on WebSocket status and containers
  useEffect(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      // WebSocket is connected, start loading containers
      if (containers.length === 0) {
        setIsContainersLoading(true);
      }
    } else {
      // WebSocket not connected, still loading
      setIsContainersLoading(true);
    }
  }, [wsConnection, currentTaskId, containers.length]);

  // Update loading state when containers are received
  useEffect(() => {
    if (containers.length > 0) {
      setIsContainersLoading(false);
    }
  }, [containers]);

  // Sync selectedContainer from context with dropdown state
  useEffect(() => {
    if (selectedContainer && containers.length > 0) {
      const containerObj = containers.find(c => c.name === selectedContainer);
      if (containerObj) {
        setSelectedContainerFromDropdown(containerObj);
      }
    }
  }, [selectedContainer, containers]);

  // Helper function to get the selected container object (similar to PreviewPanel)
  const getSelectedContainerObj = useCallback(() => {
    return containers.find(c => c.name === selectedContainer);
  }, [containers, selectedContainer]);

  // Check if the selected container is running
  const isContainerRunning = useMemo(() => {
    const container = getSelectedContainerObj();
    return container?.status === 'running';
  }, [getSelectedContainerObj]);

  // No need to monitor isDeploymentConfigOpen changes

  // Add status map with dot colors and text colors
  const statusMap = {
    running: {
      text: "Running",
      dotColor: "bg-green-500",
      textColor: "text-green-500",
      outlineColor: "outline-green-500/40",
    },
    paused: {
      text: "Paused",
      dotColor: "bg-yellow-500",
      textColor: "text-yellow-500",
      outlineColor: "outline-yellow-500/40",
    },
    submitted: {
      text: "Submitted",
      dotColor: "bg-blue-500",
      textColor: "text-blue-500",
      outlineColor: "outline-blue-500/40",
    },
    failed: {
      text: "Failed",
      dotColor: "bg-red-500",
      textColor: "text-red-500",
      outlineColor: "outline-red-500/40",
    },
    in_progress: {
      text: "In Progress",
      className:
        "px-2 py-1 typography-caption font-weight-medium bg-primary-100 text-primary-800 rounded-md shadow-sm",
    },
    stopped: {
      text: "Stopped",
      dotColor: "bg-gray-500",
      textColor: "text-gray-500",
      outlineColor: "outline-gray-500/40",
    },
  };

  // Add TaskStatus component
  const TaskStatus = ({ taskStatus }) => {
    const normalizedStatus = String(taskStatus || '').toLowerCase().replace(/\s+/g, "_");
    const status = statusMap[normalizedStatus] || statusMap["in_progress"];

    return (
      <div className="px-1.5 py-0.5 rounded-md inline-flex justify-start items-center gap-1.5">
        <div className={`w-1.5 h-1.5 ${status.dotColor} rounded-full outline outline-[3px] ${status.outlineColor}`} />
        <div className={`text-center justify-center ${status.textColor} text-xs font-medium font-['Inter'] leading-none`}>
          {status.text}
        </div>
      </div>
    );
  };

  // Add a handler for model selection
  const handleModelSelect = (modelId) => {
    setLlmModel(modelId);

    // Send model selection to the server
    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(
        JSON.stringify({
          type: "set_model",
          task_id: currentTaskId,
          model: modelId,
          user_id: Cookies.get('userId')
        })
      );
    }
  };

  // Request available models and current model when system is ready
  useEffect(() => {
    if (
      isReady &&
      wsConnection?.readyState === WebSocket.OPEN &&
      currentTaskId
    ) {
      // Set loading state
      setIsLoadingModels(true);

      // Request available models
      wsConnection.send(
        JSON.stringify({
          type: "get_available_models",
          task_id: currentTaskId,
          user_id: Cookies.get('userId')
        })
      );

      // Request current model
      wsConnection.send(
        JSON.stringify({
          type: "get_current_model",
          task_id: currentTaskId,
          user_id: Cookies.get('userId')
        })
      );

    }
  }, [isReady, wsConnection, currentTaskId]);

  // Add a direct reset function for immediate loader stopping
  const forceResetAllStates = useCallback(() => {
    setActionLoading(false);
    setIsTerminating(false);
    setIsUpdating(false);
    setIsMerging(false);
    setIsMergeAndTerminate(false);
    if (terminateTimeoutId) {
      clearTimeout(terminateTimeoutId);
      setTerminateTimeoutId(null);
      activeTimersRef.current.delete(terminateTimeoutId);
    }
  }, [terminateTimeoutId, isTerminating, isUpdating, isMerging]);

  // Handle WebSocket responses for models and status updates
  useEffect(() => {
    if (!wsConnection) return;

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Handle status updates from various WebSocket message types
        if (data.type === "status_update" && data.status) {
          setCurrentTaskStatus(data.status);
        }

        // Handle task status from other message types
        if (data.task_status) {
          setCurrentTaskStatus(data.task_status);
        }

        // Handle status from data object
        if (data.data && data.data.status) {
          setCurrentTaskStatus(data.data.status);
        }

        // Handle error messages - especially agent_not_found during termination
        if (data.type === "error" && data.status === "agent_not_found" && (isTerminating || isMerging)) {

          // Force reset all loading states
          forceResetAllStates();

          // Clear any active timeouts
          if (terminateTimeoutId) {
            clearTimeout(terminateTimeoutId);
            setTerminateTimeoutId(null);
            activeTimersRef.current.delete(terminateTimeoutId);
          }

          // Reset deployment context states
          setIsDeploymentConfigOpen(false);
          setIsDeployPanelOpen(false);
          setIsDeploying(false);
          setShowDeploymentSuccess(false);
          setDeploymentWsConnection(null);

          // Close modal immediately
          setShowConfirmModal(false);
          closeModal();


          return; // Exit early to prevent further processing
        }

        // Handle other error messages during termination operations
        if (data.type === "error" && (isTerminating || isMerging)) {


          // Force reset all loading states
          forceResetAllStates();

          // Clear any active timeouts
          if (terminateTimeoutId) {
            clearTimeout(terminateTimeoutId);
            setTerminateTimeoutId(null);
            activeTimersRef.current.delete(terminateTimeoutId);
          }

          // Reset deployment context states
          setIsDeploymentConfigOpen(false);
          setIsDeployPanelOpen(false);
          setIsDeploying(false);
          setShowDeploymentSuccess(false);
          setDeploymentWsConnection(null);

          // Close modal immediately
          setShowConfirmModal(false);
          closeModal();

          // Show error message
          const errorMessage = data.message || "Unknown error occurred during termination";
          showAlert(`Session terminated - ${errorMessage}`, "error");

          if (isMergeAndTerminate) {
            setIsMergeAndTerminate(false);
          }

          return; // Exit early to prevent further processing
        }

        // Handle stop message - wait 5 seconds then close modal
        if (data.type === "stop") {
          console.log("Received stop message from WebSocket - closing modal in 5 seconds");

          // Set flag to prevent immediate closure from status_update handler
          setIsHandlingStopMessage(true);

          // Wait for 5 seconds after receiving stop message, then close modal
          const stopDelayTimeoutId = setTimeout(async () => {
            if (isComponentMountedRef.current) {
              console.log("5 seconds elapsed after stop message - closing modal");
              try {
                const finalSessionName = sessionInputValue.trim() || "Untitled";
                // Update task name in background
                await updateTask(currentTaskId, { session_name: finalSessionName });

                if (isComponentMountedRef.current) {
                  setSessionName(finalSessionName);
                  lastSessionInputRef.current = finalSessionName;
                }
              } catch (error) {
                console.error("Background task update failed:", error);
              }

              // Reset deployment context states
              setIsDeploymentConfigOpen(false);
              setIsDeployPanelOpen(false);
              setIsDeploying(false);
              setShowDeploymentSuccess(false);
              setDeploymentWsConnection(null);

              // Reset loading states
              setIsTerminating(false);
              setActionLoading(false);
              setShowConfirmModal(false);
              setIsHandlingStopMessage(false);

              // Close the modal
              closeModal();
            }
            activeTimersRef.current.delete(stopDelayTimeoutId);
          }, 5000); // 5 second delay

          addTimerToCleanup(stopDelayTimeoutId);
          return; // Exit early to prevent further processing
        }

        // Handle model responses
        if (data.type === "available_models" && data.data?.models) {
          setAvailableModels(data.data.models);
          setIsLoadingModels(false);
        } else if (data.type === "current_model" && data.data?.model) {
          setLlmModel(data.data.model);
          setIsLoadingModels(false);
        } else if (data.type === "status_update") {
          // Log all status updates for debugging







          const status = data.data?.status || data.status;
          const isStoppedStatus = status && (
            String(status).toLowerCase() === "stopped" ||
            String(status).toLowerCase() === "stop" ||
            String(status).toLowerCase() === "terminated"
          );




          if (isStoppedStatus) {
            // If we're already handling a stop message, don't close immediately
            if (isHandlingStopMessage) {
              console.log("Stopped status received but already handling stop message - waiting for 5 second delay");
              return; // Let the stop message handler complete its 5-second delay
            }

            // Immediately call force reset to stop all loaders
            forceResetAllStates();

            // Clear timeout regardless of isTerminating state
            if (terminateTimeoutId) {

              clearTimeout(terminateTimeoutId);
              setTerminateTimeoutId(null);
              activeTimersRef.current.delete(terminateTimeoutId);
            }

            // Force reset loading states if we're in termination process
            if (isTerminating || isUpdating || showConfirmModal || isMergeAndTerminate) {



              const finalSessionName = sessionInputValue.trim() || "Untitled";

              // Reset deployment context states
              setIsDeploymentConfigOpen(false);
              setIsDeployPanelOpen(false);
              setIsDeploying(false);
              setShowDeploymentSuccess(false);
              setDeploymentWsConnection(null);

              // Close modal immediately
              setShowConfirmModal(false);
              closeModal();

              // Reset merge flag if this was after a merge
              if (isMergeAndTerminate) {
                setIsMergeAndTerminate(false); // Reset the flag
              }

              // Update task with final session name in background (don't wait for it)

              updateTask(currentTaskId, { session_name: finalSessionName })
                .then(() => {

                  if (isComponentMountedRef.current) {
                    setSessionName(finalSessionName);
                    lastSessionInputRef.current = finalSessionName;
                  }
                })
                .catch((error) => {
                  console.error("Background task update failed:", error);
                  // Don't show error to user since modal is already closed
                });

            } else {

            }
          }
        } else if (data.type === "app_state") {
          // Simply listen for app_state messages and update UI accordingly
          if (data.data?.url) {
            // Set preview URL when received from websocket
            dispatchUiState({
              type: 'BATCH_UPDATE',
              updates: {
                previewUrl: data.data.url,
                isPreviewLoading: false,
              }
            });

            // Also update showUrl if we're on the Preview tab
            if (activeTab === "Preview") {
              dispatchUiState({
                type: 'BATCH_UPDATE',
                updates: { showUrl: data.data.url }
              });
            }
          } else if (data.data?.error) {
            dispatchUiState({
              type: 'BATCH_UPDATE',
              updates: { previewError: data.data.error, isPreviewLoading: false }
            });
          }
        }
      } catch (error) {
        // Silently handle WebSocket message parsing errors
      }
    };

    wsConnection.addEventListener("message", handleMessage);

    return () => {
      wsConnection.removeEventListener("message", handleMessage);
    };
  }, [wsConnection, activeTab, setLlmModel, isTerminating, sessionInputValue, currentTaskId, setSessionName, setIsDeploymentConfigOpen, setIsDeployPanelOpen, setIsDeploying, setShowDeploymentSuccess, setDeploymentWsConnection, showAlert, closeModal, terminateTimeoutId, showConfirmModal, isUpdating, forceResetAllStates, isMergeAndTerminate, setCurrentTaskStatus, addTimerToCleanup, isHandlingStopMessage]);

  // Add a function to toggle Swagger view
  const toggleSwaggerView = () => {
    setIsSwaggerViewActive(!isSwaggerViewActive);
  };

  // Add container selection handler
  const handleContainerSelectFromDropdown = (container) => {
    setSelectedContainerFromDropdown(container);

    // Also update the context's selected container to keep them in sync
    if (container && container.name && setSelectedContainer) {
      setSelectedContainer(container.name);
    }
  };

  const options = [
    {
      key: "continue",
      label: "Continue Session",
      description: "Session remains active for future work. Branch stays separate.",
      actionText: "Continue Session",
      handler: confirmCloseModal,
    },
    {
      key: "discard",
      label: "Discard & Exit",
      description: "End session immediately. All branch changes will be lost.",
      actionText: "Discard & Exit",
      handler: handleStopAndClose,
    },
    {
      key: "save",
      label: "Save & Exit",
      description: "Merge branch to kavia-main, then close session.",
      actionText: "Save & Exit",
      handler: handleMerge,
      disabled: String(currentTaskStatus || '').toLowerCase().replace(/[-\s]/g, '_') === "stopped" || String(currentTaskStatus || '').toLowerCase().replace(/[-\s]/g, '_') === "completed",
    },
  ];

  // Auto-select a different option if the currently selected option becomes disabled
  useEffect(() => {
    const selected = options.find(opt => opt.key === selectedOption);
    if (selected && selected.disabled) {
      // Find the first non-disabled option
      const firstAvailableOption = options.find(opt => !opt.disabled);
      if (firstAvailableOption) {
        setSelectedOption(firstAvailableOption.key);
      }
    }
  }, [currentTaskStatus, selectedOption, options]);

  const selected = options.find(opt => opt.key === selectedOption);

  // Add a listener for deployment completion events
  useEffect(() => {
    const handleDeploymentComplete = (event) => {
      if (event.detail?.openHistory) {
        console.log('Deployment complete event received, opening history panel...');
        // Force close any open deployment config modal
        setIsDeploymentConfigOpen(false);
        // Set a small timeout to ensure UI state is updated
        setTimeout(() => {
          // Open the deployments modal and fetch deployments
          setShowDeploymentsModal(true);
          fetchDeployments();
          // Set flag to prevent immediate closing
          setJustOpenedDeploymentHistory(true);
          // Reset flag after a delay
          setTimeout(() => {
            setJustOpenedDeploymentHistory(false);
          }, 1500);
        }, 100);
      }
    };

    window.addEventListener('deploymentComplete', handleDeploymentComplete);
    return () => {
      window.removeEventListener('deploymentComplete', handleDeploymentComplete);
    };
  }, [fetchDeployments, setIsDeploymentConfigOpen, setShowDeploymentsModal, setJustOpenedDeploymentHistory]);

  // Extract container selector component
  const ContainerSelector = React.memo(({
    containers,
    selectedContainer,
    onContainerSelect,
    isLoading
  }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);

    const handleContainerSelect = (container) => {
      onContainerSelect(container);
      setIsOpen(false);
    };

    // Add click outside handler for container selector
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      };

      if (isOpen) {
        document.addEventListener("mousedown", handleClickOutside);
      }

      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, [isOpen]);

    return (
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-between w-full min-w-[180px] px-3 py-2 bg-white border border-gray-300 rounded-lg hover:border-gray-400 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200 text-sm text-gray-800 shadow-sm"
        >
          {selectedContainer ? selectedContainer.name : "Select Container"}
          <svg
            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 mt-2 w-full min-w-[240px] bg-white rounded-lg shadow-xl z-50 overflow-hidden border border-gray-100">
            <div className="py-1">
              {isLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin text-primary-500" />
                  <span className="ml-2 text-sm text-gray-500">Loading containers...</span>
                </div>
              ) : containers && containers.length > 0 ? (
                <div>
                  {containers.map((container, index) => (
                    <button
                      key={index}
                      onClick={() => handleContainerSelect(container)}
                      className={`w-full text-left px-4 py-2.5 text-sm transition-all duration-150 ${selectedContainer?.name === container.name
                        ? 'bg-orange-50 border-l-4 border-primary text-gray-900'
                        : 'hover:bg-gray-50 border-l-4 border-transparent text-gray-700 hover:border-gray-200'
                        }`}
                    >
                      <div className="font-medium">{container.name}</div>
                      {container.type && (
                        <div className="text-xs text-gray-500 mt-1 capitalize">
                          {container.type}
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-sm text-gray-500">
                  No containers available
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  });

  ContainerSelector.displayName = 'ContainerSelector';

  return (
    <>
      <GitSidebar
        isOpen={isSidebarOpen}
        onClose={closeSidebar}
        repository={currentRepository || {}}
        isConnected={true}
        username="fe-dev"
      />
      <TaskProgress
        isOpen={isTaskProgressOpen}
        onClose={() => setIsTaskProgressOpen(false)}
        tasks={tasks}
      />
      <DeploymentInterface
        isOpen={isDeploymentConfigOpen}
        onClose={() => {
          // Ensure context states are properly reset
          setIsDeploymentConfigOpen(false);

          // If deployment was canceled, also reset deployment progress state
          if (isDeploying) {
            setIsDeploying(false);
          }
        }}
        onOpenDeploymentHistory={() => {
          console.log('Opening deployment history panel...');
          setShowDeploymentsModal(true);
          // Fetch deployments when opening the history panel
          fetchDeployments();
          // Set flag to prevent immediate closing
          setJustOpenedDeploymentHistory(true);
          // Reset flag after a delay
          setTimeout(() => {
            setJustOpenedDeploymentHistory(false);
          }, 1000);
          console.log('Deployment history panel should now be visible');
        }}
      />
      <div className="fixed inset-0 bg-white bg-opacity-50 z-50" />
      <div className="fixed discussion-modal inset-0 bg-gray-800 bg-opacity-75 flex justify-center items-center z-50 w-full h-full">
        <div
          className={`bg-white w-[95%] h-[95%] p-2 rounded-md flex flex-col relative`}
        >
          <div className="w-full h-[5%] rounded-t-md bg-white flex items-center px-3 py-2 justify-between relative">
            {/* Orange gradient in top right */}
            <div className="absolute top-0 right-0 w-96 h-72 rounded-full blur-[120px] pointer-events-none" style={{ backgroundColor: '#F26A1B40' }} />

            <div className="flex items-center space-x-3">
              {currentTaskId?.startsWith("deep-query-job") ? (
                <>
                  <div className="font-weight-medium typography-body-sm text-gray-800">
                    Deep Analysis
                  </div>
                  <BootstrapTooltip
                    title={`WebSocket: ${wsStatus}`}
                    placement="bottom"
                  >
                    <div
                      className={`h-2.5 w-2.5 mb-1 rounded-full animate-pulse
                      ${wsStatus === "connected"
                          ? "bg-green-500"
                          : wsStatus === "connecting"
                            ? "bg-yellow-500 "
                            : "bg-red-500"
                        }`}
                    />
                  </BootstrapTooltip>
                </>
              ) : (
                <>
                  <BootstrapTooltip
                    title={`WebSocket: ${wsStatus}`}
                    placement="bottom"
                  >
                    <div
                      className={`h-2.5 w-2.5 mb-1 rounded-full animate-pulse
                      ${wsStatus === "connected"
                          ? "bg-green-500"
                          : wsStatus === "connecting"
                            ? "bg-yellow-500 "
                            : "bg-red-500"
                        }`}
                    />
                  </BootstrapTooltip>
                  <div className="font-weight-medium typography-body-sm text-gray-800">
                    {currentTaskId?.startsWith("cm")
                      ? "Code Maintenance"
                      : "Code Generation"}
                  </div>
                </>
              )}
              {!currentTaskId?.startsWith("deep-query-job") && (
                <div onClick={() => setIsTaskProgressOpen(!isTaskProgressOpen)}>
                  <TaskProgresstext
                    completedTasks={
                      steps.filter((step) => step.status === "completed").length
                    }
                    totalTasks={steps.length}
                  />
                </div>
              )}

              {/* Add ModelSelector here */}
              {/* {!currentTaskId?.startsWith("deep-query-job") && ( */}
              <ModelSelector
                selectedModel={llmModel}
                onSelectModel={handleModelSelect}
                availableModels={availableModels}
                isDisabled={!isReady || availableModels.length === 0}
                isLoading={isLoadingModels}
              />
              {/* )} */}
            </div>

            {/* Code/Preview tabs positioned to align with preview container */}
            <div className="absolute left-[30%] ml-4">
              <div className="tab-buttons flex rounded-[6px] overflow-hidden">
                <TabButtons
                  currentTaskId={currentTaskId}
                  activeTab={activeTab}
                  toggleTab={toggleTab}
                  documentContent={documentContent}
                  newDocAlert={newDocAlert}
                  projectId={projectId}
                  setFigmaData={setFigmaData}
                  figmaData={figmaData}
                  setFigmaLoader={setFigmaLoader}
                  figmaLoader={figmaLoader}
                />
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {subscriptionData && tenantId === 'b2c' ? (
                <CreditBadge
                  planCredits={subscriptionData.planCredits}
                  organizationCost={subscriptionData.organizationCost}
                  isRefreshing={isRefreshingPlan}
                  onRefresh={handleRefreshPlan}
                />
              ) : null}
              <TaskStatus taskStatus={currentTaskStatus || taskDetails?.status || "unknown"} />

              {/* GitHub Dashboard Button */}
              {!currentTaskId?.startsWith("deep-query-job") && (
                <BootstrapTooltip title={
                  (currentTaskStatus === "completed" || currentTaskStatus === "submitted" || currentTaskStatus === "failed" || currentTaskStatus === "paused")
                    ? "Git Dashboard (Available when task is running)"
                    : "Open Git Dashboard"
                } placement="bottom">
                  <button
                    onClick={(currentTaskStatus === "completed" || currentTaskStatus === "submitted" || currentTaskStatus === "failed" || currentTaskStatus === "paused")
                      ? undefined
                      : openSidebar
                    }
                    disabled={currentTaskStatus === "completed" || currentTaskStatus === "SUBMITTED" || currentTaskStatus === "failed" || currentTaskStatus === "paused"}
                    className={`w-7 h-7 p-1.5 rounded-md flex justify-center items-center transition-colors ${(currentTaskStatus === "completed" || currentTaskStatus === "submitted" || currentTaskStatus === "failed" || currentTaskStatus === "paused")
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-white/90 cursor-pointer"
                      }`}
                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"
                        fill="#484546"
                      />
                    </svg>
                  </button>
                </BootstrapTooltip>
              )}

              {/* Deploy Button */}
              {!currentTaskId?.startsWith("deep-query-job") && (
                <div className="relative deployments-dropdown">
                  <div className="flex items-center space-x-2 px-3 h-[32px] typography-caption font-weight-medium bg-primary text-white hover:bg-primary-600 rounded-md shadow-sm transition-colors">
                    <button onClick={handleShowDeployments} className="">
                      Deploy
                      {deployments.length != 0 ? ` (${deployments.length})` : ""}
                    </button>
                    <BootstrapTooltip
                      title={isContainerRunning ? "Select container for deployment" : "Preview must be running to deploy"}
                      placement="bottom"
                    >
                      <button
                        onClick={isContainerRunning ? handleShowContainerDetails : undefined}
                        disabled={!isContainerRunning}
                        className={`w-7 h-7 border-l border-gray-300 flex justify-center items-center transition-colors ${
                          isContainerRunning
                            ? "text-white hover:bg-white/10 cursor-pointer"
                            : "text-white cursor-not-allowed "
                        }`}
                      >
                        <Plus size={16} strokeWidth={2.75} />
                      </button>
                    </BootstrapTooltip>
                  </div>
                  {showDeploymentsModal && (
                    <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-[1002]">
                      <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                            <span className="w-2 h-2 rounded-full bg-primary"></span>
                            Deployment History
                          </h3>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setIsLoadingDeployments(true);
                                listDeployments(projectId)
                                  .then((response) => {
                                    const sortedDeployments = response.sort(
                                      (a, b) => new Date(b.created_at) - new Date(a.created_at)
                                    );
                                    setDeployments(sortedDeployments);
                                  })
                                  .catch((error) => {
                                    showAlert("Failed to fetch deployments", "error");
                                  })
                                  .finally(() => {
                                    setIsLoadingDeployments(false);
                                  });
                              }}
                              className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                              title="Refresh deployments"
                            >
                              <RefreshCw size={16} className={`${isLoadingDeployments ? 'animate-spin' : ''}`} />
                            </button>
                            <button
                              onClick={() => setShowDeploymentsModal(false)}
                              className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                            >
                              <X size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto p-4">
                        {isLoadingDeployments ? (
                          <div className="flex justify-center items-center py-4">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="ml-2 text-gray-600">Loading deployments...</span>
                          </div>
                        ) : deployments.length === 0 ? (
                          <div className="text-center py-8 px-4">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                              <ExternalLink className="h-8 w-8 text-gray-400" />
                            </div>
                            <p className="text-base font-medium text-gray-700">No deployments found</p>
                            <p className="text-sm text-gray-500 mt-1">There are no deployments for this session yet.</p>
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {deployments.map((deployment) => (
                              <div
                                key={deployment.deployment_id}
                                className="bg-white rounded-lg p-3 border border-gray-200 hover:border-primary-200 transition-all duration-200 cursor-pointer"
                                onClick={() => {
                                  let deploymentUrl;
                                  if (deployment.type === "backend") {
                                    // For backend deployments, use service_url if status is success
                                    deploymentUrl = deployment.status === "success" ? deployment.service_url : null;
                                  } else {
                                    // For frontend deployments, use custom_domain or app_url
                                    deploymentUrl = deployment.custom_domain || deployment.app_url;
                                  }
                                  if (deploymentUrl) {
                                    window.open(deploymentUrl, '_blank');
                                  }
                                }}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate text-left">
                                      {deployment.service_name || (deployment.root_path
                                        ? deployment.root_path.split("/").filter(Boolean).pop()
                                        : "Unknown Folder")}
                                    </p>
                                    <div className="flex items-center gap-2 mt-1">
                                      <p className="text-xs text-gray-500 flex items-center gap-1 text-left">
                                        <span className="inline-block w-2 h-2 rounded-full bg-gray-300"></span>
                                        <span className="truncate">{deployment.branch_name || "Default Branch"}</span>
                                      </p>
                                      {((deployment.type === "backend" && deployment.status === "success" && deployment.service_url) ||
                                        (deployment.type !== "backend" && (deployment.custom_domain || deployment.app_url))) && (
                                          <a
                                            href={deployment.type === "backend" ? deployment.service_url : (deployment.custom_domain || deployment.app_url)}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary hover:text-primary-700 flex-shrink-0"
                                            onClick={(e) => e.stopPropagation()}
                                            title={deployment.type === "backend" ? deployment.service_url : (deployment.custom_domain || deployment.app_url)}
                                          >
                                            <ExternalLink size={12} />
                                          </a>
                                        )}
                                    </div>
                                  </div>
                                  <span className={`flex-shrink-0 px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap border ${deployment.status === "build_success"
                                    ? "bg-blue-50 text-blue-700 border-blue-200"
                                    : deployment.status === "build_failed"
                                      ? "bg-red-50 text-red-700 border-red-200"
                                      : deployment.status === "building"
                                        ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                        : deployment.status === "deploying"
                                          ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                          : deployment.status === "success" && deployment.type === "backend"
                                            ? "bg-green-50 text-green-700 border-green-200"
                                            : deployment.status === "success" && deployment.domain_status
                                              ? deployment.domain_status === "available"
                                                ? "bg-green-50 text-green-700 border-green-200"
                                                : deployment.domain_status === "inprogress"
                                                  ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                                  : "bg-gray-50 text-gray-700 border-gray-200"
                                              : deployment.status === "success"
                                                ? "bg-green-50 text-green-700 border-green-200"
                                                : deployment.status === "processing"
                                                  ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                                  : "bg-red-50 text-red-700 border-red-200"
                                    }`}>
                                    {deployment.status === "build_success"
                                      ? "Build Success"
                                      : deployment.status === "build_failed"
                                        ? "Build Failed"
                                        : deployment.status === "building"
                                          ? "Building"
                                          : deployment.status === "deploying"
                                            ? "Deploying"
                                            : deployment.status === "success" && deployment.type === "backend"
                                              ? "Success"
                                              : deployment.status === "success" && deployment.domain_status === "inprogress"
                                                ? "Processing"
                                                : deployment.status === "failed" || deployment.status === "error"
                                                  ? "Failed"
                                                  : deployment.status}
                                  </span>
                                </div>
                                {deployment.status === "success" && deployment.type === "backend" && (
                                  <div className="flex items-center gap-2 mt-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <p className="text-xs text-green-700 font-medium text-left">
                                      Your app is live now
                                    </p>
                                  </div>
                                )}
                                {deployment.status === "success" && deployment.type !== "backend" && deployment.domain_status === "inprogress" && (
                                  <div className="flex items-center gap-2 mt-2">
                                    <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                                    <p className="text-xs text-yellow-700 font-medium text-left">
                                      Domain is processing
                                    </p>
                                  </div>
                                )}
                                {deployment.status === "success" && deployment.type !== "backend" && (deployment.domain_status === "success" || deployment.domain_status === "available") && (
                                  <div className="flex items-center gap-2 mt-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <p className="text-xs text-green-700 font-medium text-left">
                                      Your app is live now
                                    </p>
                                  </div>
                                )}
                                <div className="mt-2 text-xs text-gray-500 text-left">
                                  {new Date(deployment.created_at).toLocaleDateString()}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Container Details Panel */}
                  {showContainerDetailsPanel && (
                    <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-[1002]">
                      <div className="p-3 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                          <h3 className="typography-body-sm font-weight-medium text-gray-900">
                            Select Container for Deployment
                          </h3>
                          <button
                            onClick={() => setShowContainerDetailsPanel(false)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <X size={14} />
                          </button>
                        </div>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto p-3">
                        {isLoadingManifest ? (
                          <div className="text-center py-8">
                            <div className="mb-3">
                              <div className="inline-block h-6 w-6 rounded-full border-2 border-primary border-t-transparent animate-spin mx-auto"></div>
                            </div>
                            <p className="typography-body-sm text-gray-500 mb-2">
                              Loading container information...
                            </p>
                            <p className="typography-caption text-gray-400">
                              Fetching deployment manifest from project
                            </p>
                          </div>
                        ) : manifest && (manifest.backend || manifest.frontend) ? (
                          <div className="space-y-2">
                            {/* Backend Container */}
                            {manifest.backend && (
                              <div
                                className="bg-gray-50 rounded-md p-3 border border-gray-200 hover:bg-gray-100 transition-colors hover:border-primary-300 cursor-pointer"
                                onClick={() => handleContainerSelect(manifest.backend)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="w-2 h-2 rounded-full bg-primary"></div>
                                      <p className="typography-body-sm font-weight-medium text-gray-900">
                                        {manifest.backend.container_name}
                                      </p>
                                      <span className="px-2 py-1 typography-caption bg-primary-100 text-primary-800 rounded-full">
                                        Backend
                                      </span>
                                    </div>
                                    <p className="typography-caption text-gray-500 mb-1">
                                      Framework: {manifest.backend.framework}
                                    </p>
                                    {manifest.backend.ports && (
                                      <p className="typography-caption text-gray-500">
                                        Port: {manifest.backend.ports}
                                      </p>
                                    )}
                                    {manifest.backend.container_details?.features && (
                                      <p className="typography-caption text-gray-500 mt-1">
                                        Features: {manifest.backend.container_details.features.slice(0, 2).join(', ')}
                                        {manifest.backend.container_details.features.length > 2 && '...'}
                                      </p>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Rocket size={14} className="text-primary" />
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Frontend Container */}
                            {manifest.frontend && (
                              <div
                                className="bg-gray-50 rounded-md p-3 border border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors hover:border-primary-300"
                                onClick={() => handleContainerSelect(manifest.frontend)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                      <p className="typography-body-sm font-weight-medium text-gray-900">
                                        {manifest.frontend.container_name}
                                      </p>
                                      <span className="px-2 py-1 typography-caption bg-green-100 text-green-800 rounded-full">
                                        Frontend
                                      </span>
                                    </div>
                                    <p className="typography-caption text-gray-500 mb-1">
                                      Framework: {manifest.frontend.framework}
                                    </p>
                                    {manifest.frontend.ports && (
                                      <p className="typography-caption text-gray-500">
                                        Port: {manifest.frontend.ports}
                                      </p>
                                    )}
                                    {manifest.frontend.container_details?.features && (
                                      <p className="typography-caption text-gray-500 mt-1">
                                        Features: {manifest.frontend.container_details.features.slice(0, 2).join(', ')}
                                        {manifest.frontend.container_details.features.length > 2 && '...'}
                                      </p>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Rocket size={14} className="text-primary" />
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <div className="mb-3">
                              <Rocket size={32} className="text-gray-400 mx-auto" />
                            </div>
                            <p className="typography-body-sm text-gray-500 mb-2">
                              No containers available
                            </p>
                            <p className="typography-caption text-gray-400">
                              No deployment containers found in project manifest
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* More Dropdown Button */}
              {tenantId !== "b2c" ?
                <div className="relative more-dropdown">
                  <BootstrapTooltip title="More options" placement="bottom">
                    <button
                      onClick={() => setShowMoreDropdown(!showMoreDropdown)}
                      className="w-7 h-7 p-1.5 bg-white/80 rounded-md flex justify-center items-center transition-colors hover:bg-white/90"
                    >
                      <MoreVertical size={16} />
                    </button>
                  </BootstrapTooltip>

                  {showMoreDropdown && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                      <div className="py-1">
                        <button
                          onClick={() => {
                            handleDownloadLogs();
                            setShowMoreDropdown(false);
                          }}
                          className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 transition-colors"
                        >
                          <Download className="w-4 h-4" />
                          Download Logs
                        </button>
                      </div>
                    </div>
                  )}
                </div> : null}


              {/* Close Button */}
              <BootstrapTooltip title="Close" placement="bottom">
                <button
                  onClick={handleConfirmClose}
                  className="w-7 h-7 p-1.5 rounded-md flex justify-center items-center transition-colors hover:bg-white/90 flex-shrink-0"
                  style={{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }}
                >
                  <X size={16} />
                </button>
              </BootstrapTooltip>
            </div>
          </div>
          <div className="flex h-[93%] overflow-hidden">
            <div className="flex flex-grow overflow-hidden px-3 py-2 pb-0">
              {/* Left Panel */}
              <div
                className={`
    ${isPanelExpanded
                    ? "min-w-[100%] max-w-[100%]"
                    : "min-w-[30%] max-w-[30%]"
                  }
    transition-all duration-300 ease-in-out relative overflow-hidden
  `}
              >
                {/* Toggle Buttons */}
                <div
                  className="absolute -right-4 top-1/2 transform -translate-y-1/2 z-10"
                >
                  {/* Button code (commented out in your example) */}
                </div>

                {/* Panel Content */}
                <div
                  className={`
      transition-all duration-300 ease-in-out h-full rounded-lg bg-white opacity-100 visible
    `}
                  style={{
                    transitionDelay: "0ms",
                  }}
                >
                  {/* Code generation chat panel */}
                  <CodeDiscussionPanel isPanelExpanded={isPanelExpanded} isStopped={currentTaskStatus === "stopped" || taskDetails?.status === "stopped"} />
                </div>
              </div>

              {/* Right Panel */}
              <div
                className={`flex-grow overflow-auto justify-between transition-all duration-300 ease-in-out ${isFullscreen ? "fixed inset-0 z-50 bg-white p-4" : ""
                  }`}
                ref={rightPanelRef}
                style={{ backgroundColor: '#ffffff !important', opacity: 1 }}
              >
                <div className="flex flex-col h-full border border-gray-200 rounded-lg ml-4" style={{ backgroundColor: '#ffffff !important', opacity: 1, }}>
                  {/* Tabs Header */}
                  <div
                    className={`tab-header px-2 rounded-t-lg flex items-center justify-between border border-[#DEDEDE] ${activeTab === 'Preview' ? 'py-1' : 'py-[8px]'
                      }`}
                    style={{ backgroundColor: '#ffffff', opacity: 1 }}
                  >
                    {/* LEFT: Container Selector and Preview containers */}
                    <div className="flex-1 flex items-center gap-3">
                      {(activeTab === "Preview" || activeTab === "Containers") && (
                        <ContainerSelector
                          containers={availableContainers}
                          selectedContainer={selectedContainerFromDropdown}
                          onContainerSelect={handleContainerSelectFromDropdown}
                          isLoading={isContainersLoading}
                        />
                      )}
                      {activeTab === "Preview" && (
                        <PreviewContainers />
                      )}
                    </div>

                    {/* CENTER: URL display with refresh and copy buttons */}
                    <div className="flex-1 flex justify-center">
                      <UrlDisplaySection
                        currentTaskId={currentTaskId}
                        activeTab={activeTab}
                        showUrl={showUrlRef.current}
                        handleRefresh={handleRefresh}
                        handleCopy={handleCopy}
                        copied={copied}
                      />
                    </div>

                    {/* RIGHT: Action buttons */}
                    <div className="flex-1 flex justify-end">
                      <TabActionButtons
                        currentTaskId={currentTaskId}
                        activeTab={activeTab}
                        showUrl={showUrlRef.current}
                        activeView={activeView}
                        setActiveView={setActiveView}
                        isSwaggerViewActive={isSwaggerViewActive}
                        toggleSwaggerView={toggleSwaggerView}
                        isFullscreen={isFullscreen}
                        toggleFullscreen={toggleFullscreen}
                      />
                    </div>
                  </div>
                  {/* Tab Content */}
                  <div className={`flex-grow ${activeTab === "Preview" ? "overflow-hidden" : "overflow-auto"}`} style={{ backgroundColor: '#ffffff', opacity: 1 }}>
                    <div className="tab-content h-full w-full relative overflow-hidden" style={{ backgroundColor: '#ffffff', opacity: 1 }}>
                      <TabContent
                        currentTaskId={currentTaskId}
                        activeTab={activeTab}
                        documentContent={documentContent}
                        isSwaggerViewActive={isSwaggerViewActive}
                        formatWorkspaceUrl={formatWorkspaceUrl}
                        currentIp={currentIp}
                        isPreviewLoading={uiState.isPreviewLoading}
                        previewError={uiState.previewError}
                        handlePreviewRefresh={handlePreviewRefresh}
                        handlePortChange={handlePortChange}
                        portNumber={portNumber}
                        iframeKey={iframeKey}
                        previewUrl={uiState.previewUrl}
                        currentUrl={uiState.currentUrl}
                        activeView={activeView}
                        isLoading={isLoading}
                        setIsLoading={setIsLoading}
                        projectId={projectId}
                        figmaData={figmaData}
                        figmaLoader={figmaLoader}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification.show && (
        <div className="fixed bottom-10 right-10 z-[60] animate-fade-in">
          <div className="bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 flex items-center justify-center z-[100]">
          {/* Backdrop overlay */}
          <div
            className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm"
            onClick={() => setShowConfirmModal(false)}
          />

          {/* Modal container */}
          <div
            className={`bg-white rounded-lg shadow-xl max-w-md w-full p-6 z-[70] relative `}
          >
            {/* Close button */}
            <button
              onClick={() => setShowConfirmModal(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={18} />
            </button>

            {/* Modal content */}
            <div className="space-y-5">
              {/* Title and description */}
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">Close Session</h3>
                <p className="mt-2 text-sm text-gray-500">Choose how you'd like to handle this session</p>
                {isAiTyping && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      ⚠️ "Discard & Exit" and "Save & Exit" options are disabled while content is streaming. You can still continue the session.
                    </p>
                  </div>
                )}
              </div>
              {/* Session name and warning only for non-discard options */}

              <>
                <div className="space-y-2 mb-4">
                  <label className="block typography-body-sm font-weight-medium text-gray-700">
                    Session name (optional)
                  </label>
                  <input
                    type="text"
                    value={sessionInputValue}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      setSessionInputValue(newValue);
                      lastSessionInputRef.current = newValue;
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 transition-colors focus:border-primary focus:ring-primary-500"
                    placeholder="Name your session"
                  />
                </div>
                {/* <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <FaInfoCircle className="h-5 w-5 text-primary-500" />
                    </div>
                    <div className="ml-3">
                      <p className="typography-body-sm text-gray-700">
                        {currentTaskId?.startsWith("deep-query-job")
                          ? "Make sure to save important query results before closing."
                          : "Make sure to commit and push your code using the Git Dashboard before closing."}
                      </p>
                    </div>
                  </div>
                </div> */}
              </>

              <div className="space-y-3">
                {options.map(opt => {
                  if (!currentTaskId?.startsWith('deep-query-job') || currentTaskId?.startsWith('deep-query-job') && opt.key !== 'save') {
                    return (
                      <div
                        key={opt.key}
                        className={`flex items-start p-4 rounded-lg border transition
                        ${opt.disabled || (isAiTyping && (opt.key === 'discard' || opt.key === 'save'))
                            ? "border-gray-200 bg-gray-50 cursor-not-allowed opacity-50"
                            : selectedOption === opt.key
                              ? "border-primary bg-primary-50 cursor-pointer"
                              : "border-gray-200 bg-white hover:bg-gray-50 cursor-pointer"
                          }`}
                        onClick={() => !opt.disabled && !(isAiTyping && (opt.key === 'discard' || opt.key === 'save')) && setSelectedOption(opt.key)}
                      >
                        <input
                          type="radio"
                          checked={selectedOption === opt.key}
                          onChange={() => !opt.disabled && !(isAiTyping && (opt.key === 'discard' || opt.key === 'save')) && setSelectedOption(opt.key)}
                          disabled={opt.disabled || (isAiTyping && (opt.key === 'discard' || opt.key === 'save'))}
                          className="mt-1 accent-orange-500"
                        />
                        <div className="ml-3">
                          <div className={`font-semibold ${opt.disabled
                            ? "text-gray-400"
                            : selectedOption === opt.key
                              ? "text-primary-700"
                              : "text-gray-900"
                            }`}>
                            {currentTaskId?.startsWith('deep-query-job') && opt.key == 'discard' ? "Save & Exit" : opt.label}
                          </div>
                          <div className={`text-sm ${opt.disabled ? "text-gray-400" : "text-gray-500"}`}>
                            {opt.disabled && opt.key === 'save'
                              ? "Save & Exit is not available when the session is stopped or completed"
                              : currentTaskId?.startsWith('deep-query-job') && opt.key == 'discard'
                                ? "Save and Close Session"
                                : opt.description}
                          </div>
                        </div>
                      </div>
                    )
                  }
                })}
              </div>
              {/* Discard warning for discard option */}

              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowConfirmModal(false)}
                  className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    console.log("Continue session button clicked");
                    if (!selected.disabled) {
                      selected.handler();
                    }
                  }}
                  disabled={actionLoading || selected.disabled || (isAiTyping && (selected.key === 'discard' || selected.key === 'save'))}
                  className="px-4 py-2 text-white bg-primary rounded-md hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {actionLoading && (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  )}
                  {currentTaskId?.startsWith('deep-query-job') && selected.key == 'discard' ? "Save & Exit" : selected.actionText}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Deployment Success Modal */}
      {showDeploymentSuccess && (
        <div className="fixed inset-0 flex items-center justify-center z-[60]">
          <div
            className="fixed inset-0 bg-gray-800 bg-opacity-75"
            onClick={() => setShowDeploymentSuccess(false)}
          />
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 z-[70] relative">
            {/* Close button */}
            <button
              onClick={() => setShowDeploymentSuccess(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={18} />
            </button>

            <div className="space-y-5">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="bg-green-100 rounded-full p-3">
                    <Rocket className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <h3 className="typography-body-lg font-weight-semibold text-gray-900">
                  Deployment Successful!
                </h3>
                <p className="mt-2 typography-body-sm text-gray-500">
                  Your application has been successfully deployed.
                </p>
              </div>

              {/* Deployment URL */}
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="typography-body-sm font-weight-medium text-gray-700 mb-2">
                  Your site is now live at:
                </p>
                <div className="flex items-center">
                  <input
                    type="text"
                    value={deployedUrl}
                    readOnly
                    className="flex-1 px-3 py-2 bg-white border border-gray-300 rounded-l-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary typography-body-sm"
                  />
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(deployedUrl);
                      showAlert("URL copied to clipboard", "success");
                    }}
                    className="px-3 py-2 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors"
                    title="Copy URL"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-gray-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-4 mt-6">
                <button
                  onClick={() => setShowDeploymentSuccess(false)}
                  className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors"
                >
                  Close
                </button>
                <a
                  href={deployedUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-4 py-2 typography-body-sm font-weight-medium text-white bg-primary hover:bg-primary-600 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors flex items-center justify-center gap-1"
                >
                  <ExternalLink size={14} />
                  Visit Site
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {deleteConfirmation && (
        <div className="fixed inset-0 flex items-center justify-center z-[60]">
          <div
            className="fixed inset-0 bg-gray-800 bg-opacity-75"
            onClick={() => setDeleteConfirmation(null)}
          />
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 z-[70] relative">
            <div className="flex items-center justify-between mb-4">
              <h3 className="typography-body-lg font-weight-semibold text-gray-900">
                Delete Deployment
              </h3>
              <button
                onClick={() => setDeleteConfirmation(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={18} />
              </button>
            </div>
            <p className="typography-body-sm text-gray-600 mb-4">
              Are you sure you want to delete this deployment? This action
              cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setDeleteConfirmation(null)}
                className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handleDeleteDeployment(
                    deleteConfirmation.deployment_id,
                    deleteConfirmation.app_id
                  );
                  setDeleteConfirmation(null);
                }}
                className="px-4 py-2 typography-body-sm font-weight-medium text-white bg-red-500 hover:bg-red-600 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      <InactivityTimerModal />
      <SessionLimitModal
        isVisible={sessionLimitModal.show}
        messageType={sessionLimitModal.messageType}
        countdownSeconds={sessionLimitModal.countdownSeconds}
        sessionInputValue={sessionInputValue}
        onMergeAndClose={handleStopAndClose}
      />
      {/* Deployment Interface */}
      <DeploymentInterface
        isOpen={isDeploymentConfigOpen}
        onClose={() => setIsDeploymentConfigOpen(false)}
        selectedContainer={selectedContainerForDeployment}
        onOpenDeploymentHistory={() => {
          console.log('Opening deployment history panel...');
          setShowDeploymentsModal(true);
          // Fetch deployments when opening the history panel
          fetchDeployments();
          // Set flag to prevent immediate closing
          setJustOpenedDeploymentHistory(true);
          // Reset flag after a delay
          setTimeout(() => {
            setJustOpenedDeploymentHistory(false);
          }, 1000);
          console.log('Deployment history panel should now be visible');
        }}
      />
    </>
  );
};

export default CodeGenerationModal;
