// hooks/useDeploymentDashboard.js
import { useState, useCallback, useRef } from 'react';
import {
  getDeploymentOverallStats,
  getDeploymentChartData,
  getDeploymentTenants,
  getProjectsByTenant,
  getDeploymentsByProject,
  getDeploymentDetails,
  updateDeploymentStatus,
  deleteDeployment
} from '@/utils/api';

export const DEPLOYMENT_VIEWS = {
  OVERVIEW: 'overview',
  TENANT: 'tenant',
  PROJECT: 'project',
  DEPLOYMENT: 'deployment'
};

export const DEPLOYMENT_STATUSES = {
  ALL: 'all',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed'
};

export const useDeploymentDashboard = () => {
  // Core state
  const [currentView, setCurrentView] = useState(DEPLOYMENT_VIEWS.OVERVIEW);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Data state
  const [overallStats, setOverallStats] = useState(null);
  const [tenants, setTenants] = useState([]);
  const [projects, setProjects] = useState([]);
  const [deployments, setDeployments] = useState([]);
  const [deploymentDetails, setDeploymentDetails] = useState(null);

  // Selection state
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [selectedProject, setSelectedProject] = useState(null);

  // Global filters
  const [globalFilters, setGlobalFilters] = useState({
    dateFrom: null,
    dateTo: null,
    status: DEPLOYMENT_STATUSES.ALL
  });

  // Loading cache to prevent duplicate calls
  const loadingCache = useRef({
    stats: false,
    tenants: false,
    projects: false,
    deployments: false,
    deploymentDetails: false
  });

  // Data cache to prevent unnecessary API calls
  const dataCache = useRef({
    stats: null,
    tenants: null,
    projects: new Map(), // tenantId -> projects data
    deployments: new Map(), // `${tenantId}-${projectId}-${filters}` -> deployments data
    deploymentDetails: new Map() // deploymentId -> deployment details
  });

  // Abort controller for cancelling requests
  const abortControllerRef = useRef(null);

  // Helper to cancel ongoing requests
  const cancelOngoingRequests = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    return abortControllerRef.current.signal;
  }, []);

  // Error handler
  const handleError = useCallback((error, context = '') => {
    console.error(`Deployment Dashboard Error${context ? ` (${context})` : ''}:`, error);
    
    if (error.name === 'AbortError') {
      return; // Don't show error for cancelled requests
    }

    setError({
      message: error.message || 'An unexpected error occurred',
      context,
      timestamp: new Date().toISOString()
    });
  }, []);

  // Format date for API (ISO string)
  const formatDateForAPI = useCallback((dateString) => {
    if (!dateString) return null;
    try {
      return new Date(dateString).toISOString();
    } catch (error) {
      console.warn('Invalid date format:', dateString);
      return null;
    }
  }, []);

  // Generate cache key for deployments
  const getDeploymentsCacheKey = useCallback((tenantId, projectId, filters) => {
    const filterString = JSON.stringify({
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo,
      status: filters.status
    });
    return `${tenantId}-${projectId}-${filterString}`;
  }, []);

  // Helper function to map frontend status to backend status
  const mapStatusToBackend = useCallback((status) => {
    if (!status || status === DEPLOYMENT_STATUSES.ALL) {
      return null;
    }
    
    switch (status) {
      case DEPLOYMENT_STATUSES.COMPLETED:
        return 'success';
      case DEPLOYMENT_STATUSES.PROCESSING:
        return 'processing';
      case DEPLOYMENT_STATUSES.FAILED:
        return 'build_failed';
      default:
        return null;
    }
  }, []);

  // Load overall stats
  const loadOverallStats = useCallback(async (filters = {}, force = false) => {
    if (loadingCache.current.stats && !force) {
      return;
    }

    const appliedFilters = { ...globalFilters, ...filters };
    const cacheKey = JSON.stringify(appliedFilters);
    
    if (!force && dataCache.current.stats === cacheKey) {
      return;
    }

    loadingCache.current.stats = true;
    setLoading(true);
    
    try {
      const statsData = await getDeploymentOverallStats(
        formatDateForAPI(appliedFilters.dateFrom),
        formatDateForAPI(appliedFilters.dateTo),
        mapStatusToBackend(appliedFilters.status)
      );
      
      dataCache.current.stats = cacheKey;
      setOverallStats(statsData);
      setError(null);
    } catch (error) {
      handleError(error, 'loading deployment statistics');
    } finally {
      loadingCache.current.stats = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI]); // Removed globalFilters from dependencies

  // Load tenants
  const loadTenants = useCallback(async (filters = {}, force = false) => {
    if (loadingCache.current.tenants && !force) {
      return;
    }

    const appliedFilters = { ...globalFilters, ...filters };
    const cacheKey = JSON.stringify(appliedFilters);
    
    if (!force && dataCache.current.tenants === cacheKey) {
      return;
    }

    loadingCache.current.tenants = true;
    setLoading(true);
    
    try {
      const tenantsData = await getDeploymentTenants(
        formatDateForAPI(appliedFilters.dateFrom),
        formatDateForAPI(appliedFilters.dateTo),
        mapStatusToBackend(appliedFilters.status)
      );
      
      dataCache.current.tenants = cacheKey;
      setTenants(tenantsData.tenants || []);
      setError(null);
    } catch (error) {
      handleError(error, 'loading tenants');
    } finally {
      loadingCache.current.tenants = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI, mapStatusToBackend]); // Removed globalFilters from dependencies

  // Load projects
  const loadProjects = useCallback(async (tenantId, filters = {}, force = false) => {
    if (!tenantId) return;

    const appliedFilters = { ...globalFilters, ...filters };
    const cacheKey = `${tenantId}-${JSON.stringify(appliedFilters)}`;
    
    if (loadingCache.current.projects && !force) {
      return;
    }

    if (!force && dataCache.current.projects.has(cacheKey)) {
      setProjects(dataCache.current.projects.get(cacheKey));
      return;
    }

    loadingCache.current.projects = true;
    setLoading(true);
    
    try {
      const projectsData = await getProjectsByTenant(
        tenantId,
        formatDateForAPI(appliedFilters.dateFrom),
        formatDateForAPI(appliedFilters.dateTo),
        mapStatusToBackend(appliedFilters.status)
      );
      
      dataCache.current.projects.set(cacheKey, projectsData.projects || []);
      setProjects(projectsData.projects || []);
      setError(null);
    } catch (error) {
      handleError(error, 'loading projects');
    } finally {
      loadingCache.current.projects = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI]); // Removed globalFilters from dependencies

  // Load deployments
  const loadDeployments = useCallback(async (tenantId, projectId, filters = {}, force = false) => {
    if (!tenantId || !projectId) return;

    const appliedFilters = { ...globalFilters, ...filters };
    const cacheKey = getDeploymentsCacheKey(tenantId, projectId, appliedFilters);
    
    if (loadingCache.current.deployments && !force) {
      return;
    }

    if (!force && dataCache.current.deployments.has(cacheKey)) {
      setDeployments(dataCache.current.deployments.get(cacheKey));
      return;
    }

    loadingCache.current.deployments = true;
    setLoading(true);
    
    try {
      const deploymentsData = await getDeploymentsByProject(
        tenantId,
        projectId,
        formatDateForAPI(appliedFilters.dateFrom),
        formatDateForAPI(appliedFilters.dateTo),
        mapStatusToBackend(appliedFilters.status)
      );
      
      dataCache.current.deployments.set(cacheKey, deploymentsData.deployments || []);
      setDeployments(deploymentsData.deployments || []);
      setError(null);
    } catch (error) {
      handleError(error, 'loading deployments');
    } finally {
      loadingCache.current.deployments = false;
      setLoading(false);
    }
  }, [handleError, formatDateForAPI, getDeploymentsCacheKey, mapStatusToBackend]); // Removed globalFilters from dependencies

  // Load deployment details
  const loadDeploymentDetails = useCallback(async (tenantId, projectId, deploymentId, force = false) => {
    if (!tenantId || !projectId || !deploymentId) return;

    if (loadingCache.current.deploymentDetails && !force) {
      return;
    }

    if (!force && dataCache.current.deploymentDetails.has(deploymentId)) {
      setDeploymentDetails(dataCache.current.deploymentDetails.get(deploymentId));
      return;
    }

    loadingCache.current.deploymentDetails = true;
    setLoading(true);
    
    try {
      const deploymentData = await getDeploymentDetails(tenantId, projectId, deploymentId);
      
      dataCache.current.deploymentDetails.set(deploymentId, deploymentData);
      setDeploymentDetails(deploymentData);
      setError(null);
    } catch (error) {
      handleError(error, 'loading deployment details');
    } finally {
      loadingCache.current.deploymentDetails = false;
      setLoading(false);
    }
  }, [handleError]);

  // Navigation functions
  const navigateToOverview = useCallback(() => {
    setCurrentView(DEPLOYMENT_VIEWS.OVERVIEW);
    setSelectedTenant(null);
    setSelectedProject(null);
    setDeploymentDetails(null);
    // Refresh stats when navigating to overview
    loadOverallStats({}, true);
    loadTenants({}, true);
  }, [loadOverallStats, loadTenants]);

  const navigateToTenant = useCallback((tenant) => {
    setCurrentView(DEPLOYMENT_VIEWS.TENANT);
    setSelectedTenant(tenant);
    setSelectedProject(null);
    setDeploymentDetails(null);
    // Refresh stats when navigating to tenant view
    loadOverallStats({}, true);
    loadProjects(tenant.tenant_id);
  }, [loadProjects, loadOverallStats]);

  const navigateToProject = useCallback((project) => {
    setCurrentView(DEPLOYMENT_VIEWS.PROJECT);
    setSelectedProject(project);
    setDeploymentDetails(null);
    // Refresh stats when navigating to project view
    loadOverallStats({}, true);
    if (selectedTenant) {
      loadDeployments(selectedTenant.tenant_id, project.project_id);
    }
  }, [selectedTenant, loadDeployments, loadOverallStats]);

  const navigateToDeployment = useCallback((deployment) => {
    setCurrentView(DEPLOYMENT_VIEWS.DEPLOYMENT);
    // Refresh stats when navigating to deployment view
    loadOverallStats({}, true);
    if (selectedTenant && selectedProject) {
      loadDeploymentDetails(
        selectedTenant.tenant_id,
        selectedProject.project_id,
        deployment.deployment_id
      );
    }
  }, [selectedTenant, selectedProject, loadDeploymentDetails, loadOverallStats]);

  // Navigate back functions
  const navigateToTenantView = useCallback(() => {
    if (selectedTenant) {
      navigateToTenant(selectedTenant);
    } else {
      navigateToOverview();
    }
    // Refresh stats when navigating back
    loadOverallStats({}, true);
  }, [selectedTenant, navigateToTenant, navigateToOverview, loadOverallStats]);

  const navigateToProjectView = useCallback(() => {
    if (selectedProject) {
      navigateToProject(selectedProject);
    } else if (selectedTenant) {
      navigateToTenant(selectedTenant);
    } else {
      navigateToOverview();
    }
    // Refresh stats when navigating back
    loadOverallStats({}, true);
  }, [selectedProject, selectedTenant, navigateToProject, navigateToTenant, navigateToOverview, loadOverallStats]);

  // Filter functions
  const applyFilters = useCallback((newFilters) => {
    setGlobalFilters(prev => ({ ...prev, ...newFilters }));
    
    // Clear relevant caches
    dataCache.current.stats = null;
    dataCache.current.tenants = null;
    dataCache.current.projects.clear();
    dataCache.current.deployments.clear();
    
    // Always reload stats with new filters
    loadOverallStats(newFilters, true);
    
    // Reload data based on current view
    if (currentView === DEPLOYMENT_VIEWS.OVERVIEW) {
      loadTenants(newFilters, true);
    } else if (currentView === DEPLOYMENT_VIEWS.TENANT && selectedTenant) {
      loadProjects(selectedTenant.tenant_id, newFilters, true);
    } else if (currentView === DEPLOYMENT_VIEWS.PROJECT && selectedTenant && selectedProject) {
      loadDeployments(selectedTenant.tenant_id, selectedProject.project_id, newFilters, true);
    }
  }, [currentView, selectedTenant, selectedProject, loadOverallStats, loadTenants, loadProjects, loadDeployments]);

  // Refresh data
  const refreshData = useCallback(() => {
    // Clear all caches
    dataCache.current.stats = null;
    dataCache.current.tenants = null;
    dataCache.current.projects.clear();
    dataCache.current.deployments.clear();
    dataCache.current.deploymentDetails.clear();
    
    // Always refresh stats regardless of current view
    loadOverallStats({}, true);
    
    // Reload based on current view
    if (currentView === DEPLOYMENT_VIEWS.OVERVIEW) {
      loadTenants({}, true);
    } else if (currentView === DEPLOYMENT_VIEWS.TENANT && selectedTenant) {
      loadProjects(selectedTenant.tenant_id, {}, true);
    } else if (currentView === DEPLOYMENT_VIEWS.PROJECT && selectedTenant && selectedProject) {
      loadDeployments(selectedTenant.tenant_id, selectedProject.project_id, {}, true);
    } else if (currentView === DEPLOYMENT_VIEWS.DEPLOYMENT && selectedTenant && selectedProject && deploymentDetails) {
      loadDeploymentDetails(
        selectedTenant.tenant_id,
        selectedProject.project_id,
        deploymentDetails.deployment_id,
        true
      );
    }
  }, [currentView, selectedTenant, selectedProject, deploymentDetails, loadOverallStats, loadTenants, loadProjects, loadDeployments, loadDeploymentDetails]);

  // Admin operations
  const updateStatus = useCallback(async (tenantId, projectId, deploymentId, newStatus) => {
    try {
      setLoading(true);
      await updateDeploymentStatus(tenantId, projectId, deploymentId, newStatus);
      
      // Clear relevant caches and reload
      dataCache.current.stats = null; // Clear stats cache to refresh counts
      dataCache.current.deployments.clear();
      dataCache.current.deploymentDetails.delete(deploymentId);
      
      // Refresh stats after status update
      loadOverallStats({}, true);
      
      if (currentView === DEPLOYMENT_VIEWS.PROJECT) {
        loadDeployments(tenantId, projectId, {}, true);
      } else if (currentView === DEPLOYMENT_VIEWS.DEPLOYMENT) {
        loadDeploymentDetails(tenantId, projectId, deploymentId, true);
      }
      
      setError(null);
    } catch (error) {
      handleError(error, 'updating deployment status');
    } finally {
      setLoading(false);
    }
  }, [currentView, loadDeployments, loadDeploymentDetails, loadOverallStats, handleError]);

  const deleteDeploymentItem = useCallback(async (tenantId, projectId, deploymentId) => {
    try {
      setLoading(true);
      await deleteDeployment(tenantId, projectId, deploymentId);
      
      // Clear relevant caches and navigate back
      dataCache.current.stats = null; // Clear stats cache to refresh counts
      dataCache.current.deployments.clear();
      dataCache.current.deploymentDetails.delete(deploymentId);
      
      // Refresh stats after deletion
      loadOverallStats({}, true);
      
      if (currentView === DEPLOYMENT_VIEWS.DEPLOYMENT) {
        navigateToProjectView();
      } else if (currentView === DEPLOYMENT_VIEWS.PROJECT) {
        loadDeployments(tenantId, projectId, {}, true);
      }
      
      setError(null);
    } catch (error) {
      handleError(error, 'deleting deployment');
    } finally {
      setLoading(false);
    }
  }, [currentView, navigateToProjectView, loadDeployments, loadOverallStats, handleError]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    currentView,
    loading,
    error,
    overallStats,
    tenants,
    projects,
    deployments,
    deploymentDetails,
    selectedTenant,
    selectedProject,
    globalFilters,

    // Navigation
    navigateToOverview,
    navigateToTenant,
    navigateToProject,
    navigateToDeployment,
    navigateToTenantView,
    navigateToProjectView,

    // Data loading
    loadOverallStats,
    loadTenants,
    loadProjects,
    loadDeployments,
    loadDeploymentDetails,

    // Filtering
    applyFilters,
    refreshData,

    // Admin operations
    updateStatus,
    deleteDeploymentItem,

    // Utilities
    clearError,
    cancelOngoingRequests
  };
};